<?php
declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON>\JWT\Validation\Constraint;

use <PERSON><PERSON><PERSON><PERSON>\JWT\Token;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Validation\Constraint;
use <PERSON>cobucci\JWT\Validation\ConstraintViolation;

final class RelatedTo implements Constraint
{
    /** @param non-empty-string $subject */
    public function __construct(private readonly string $subject)
    {
    }

    public function assert(Token $token): void
    {
        if (! $token->isRelatedTo($this->subject)) {
            throw ConstraintViolation::error(
                'The token is not related to the expected subject',
                $this,
            );
        }
    }
}
