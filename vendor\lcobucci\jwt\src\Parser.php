<?php
declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON>\JWT;

use <PERSON><PERSON><PERSON><PERSON>\JWT\Encoding\CannotDecodeContent;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Token\InvalidTokenStructure;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Token\UnsupportedHeaderFound;

interface Parser
{
    /**
     * Parses the JWT and returns a token
     *
     * @param non-empty-string $jwt
     *
     * @throws CannotDecodeContent      When something goes wrong while decoding.
     * @throws InvalidTokenStructure    When token string structure is invalid.
     * @throws UnsupportedHeaderFound   When parsed token has an unsupported header.
     */
    public function parse(string $jwt): Token;
}
