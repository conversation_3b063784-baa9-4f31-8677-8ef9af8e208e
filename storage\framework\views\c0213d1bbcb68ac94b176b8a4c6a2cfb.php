<!-- Promo Bar -->
<div class="fixed-web-header">

    <div class=" text-white py-1 discount-header">
        <div class="container d-flex justify-content-between align-items-center py-5">
            <ul class="d-flex gap-4 mb-0 px-0 header-icon">
                <li><a href="https://www.facebook.com/"><img src="<?php echo e(asset('website')); ?>/assets/images/facebook.svg"
                            alt="Logo" class="img-fluid"></a></li>
                <li><a href="https://www.whatsapp.com/"><img src="<?php echo e(asset('website')); ?>/assets/images/whatsapp.svg"
                            alt="Logo" class="img-fluid"></a></li>
                <li><a href="https://www.youtube.com/"><img src="<?php echo e(asset('website')); ?>/assets/images/youtube.svg"
                            alt="Logo" class="img-fluid"></a></li>
            </ul>
            <div class="d-flex gap-4">
                <a href="mailto:<EMAIL>" class="fs-13 normal white"><i class="fa-solid fa-envelope me-2"
                        style="color: #ffffff;"></i> <EMAIL> </a>
                <a href="tel:************" class="fs-13 normal white"> <i class="fa-solid fa-phone me-2"
                        style="color: #ffffff;"></i> ************</a>
            </div>
        </div>
    </div>

    <!-- Main Navbar -->
    <nav class="navbar navbar-expand-lg bg-white header">
        <div class="container py-2">
            <!-- Logo -->
            <div class="d-flex gap-2 w-400px align-items-center">
                <a class="navbar-brand" href="<?php echo e(url('/')); ?>">
                    
                    
                    <img src="<?php echo e(asset('website')); ?>/assets/images/logo-image.svg" alt="Logo" class="img-fluid"
                        style="height: 37px;">
                </a>
                <?php if(auth()->check() && !auth()->user()->hasRole('admin')): ?>
                    <img src="<?php echo e(asset('website')); ?>/assets/images/location.svg" alt="Logo" class="img-fluid"
                        style="height: 20px;">

                    <div class="custom-select-location" style="width:200px;">
                        <select>
                            <option value="0">Brazil, Rio De Janeiro</option>
                            <option value="1">Brazil, Rio De Janeiro</option>
                            <option value="2">Brazil, Rio De Janeiro</option>
                        </select>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Toggler for mobile -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNavbar">
                <span class="navbar-toggler-icon"></span>
            </button>


            <!-- Navbar content -->
            <div class="collapse navbar-collapse flex-end align-items-center navbar-header gap-4" id="mainNavbar">
                <?php if(auth()->check() && auth()->user()->hasRole('individual')): ?>
                    <div class="app-navbar-item ms-1 user-info">
                        <i class="fas fa-star "> <span class="user-name sora fs-12"> INDIVIDUAL </span> </i>
                    </div>
                <?php elseif(auth()->check() && auth()->user()->hasRole('business')): ?>
                    <div class="app-navbar-item ms-1 user-info">
                        <i class="fas fa-star "> <span class="user-name sora fs-12"> BUSINESS </span> </i>
                    </div>
                <?php endif; ?>

                <div class="app-navbar-item ms-1 ">
                    <div class="btn btn-icon btn-custom btn-icon-muted btn-active-light btn-active-color-primary w-35px h-35px"
                        data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-attach="parent"
                        data-kt-menu-placement="bottom-end" id="kt_menu_item_wow">
                        <i class="far fa-bell"></i>
                        <span class="path1"></span>
                        <span class="path2"></span>
                        <span class="path3"></span>
                        <span class="path4"></span>
                        </i>
                    </div>
                    <div class="menu notification-dropdown menu-sub menu-sub-dropdown menu-column w-350px w-lg-375px p-5"
                        data-kt-menu="true" id="kt_menu_notifications">

                        <p class="fs-16 bold">Notifications</p>

                        <?php for($i = 0; $i < 4; $i++): ?>
                            <div
                                class="d-flex align-items-center gap-3 justify-content-center  border-bottom mb-5 pb-3">
                                <img src="<?php echo e(asset('website')); ?>/assets/images/notification-user.png" alt="Logo"
                                    class="img-fluid" style="height: 50px;">
                                <div>
                                    <p class="fs-13 mb-0">Jenny Wilson Lorem Ipsum is simply dummy text of the printing.
                                    </p>
                                    <p class="fs-12 mb-0">5 min</p>
                                </div>
                            </div>
                        <?php endfor; ?>

                        <a href="<?php echo e(route('notification')); ?>" class="see-all-btn"> See All</a>
                    </div>
                </div>

                <div lass="app-navbar-item ms-1">
                    <i class="far fa-envelope mt-1"></i>
                </div>

                <div lass="app-navbar-item ms-1">
                    <i class="far fa-question-circle mt-1"></i>
                </div>

                <div class="app-navbar-item ms-1 " id="kt_header_user_menu_toggle">
                    <div class="cursor-pointer symbol symbol-35px "
                        data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-attach="parent"
                        data-kt-menu-placement="bottom-end">
                        <?php if(auth()->user()->profile->pic ?? '' == null): ?>
                            <img src="<?php echo e(asset('website')); ?>/assets/media/avatars/blank.png" class="rounded-pill"
                                alt="user" />
                        <?php else: ?>
                            <img alt="Logo"
                                src="<?php echo e(asset('storage/uploads/users/' . auth()->user()->profile->pic)); ?>" />
                        <?php endif; ?>
                    </div>
                    <div class="menu  menu-sub menu-sub-dropdown right-sidebar-menus menu-column menu-rounded menu-gray-800 menu-state-bg menu-state-color fw-semibold py-4 fs-6 w-275px"
                        data-kt-menu="true">
                        <div class="menu-item px-3">
                            <div class="menu-content d-flex align-items-center px-3">
                                <div class="symbol symbol-50px me-5">
                                    <?php if(auth()->user()->profile->pic ?? '' == null): ?>
                                        <img src="<?php echo e(asset('website')); ?>/assets/media/avatars/blank.png"
                                            class="rounded-pill" alt="user" />
                                    <?php else: ?>
                                        <img alt="Logo"
                                            src="<?php echo e(asset('storage/uploads/users/' . auth()->user()->profile->pic)); ?>" />
                                    <?php endif; ?>
                                </div>
                                <div class="d-flex flex-column">
                                    <div class="fw-bold d-flex align-items-center fs-5"><?php echo e(Auth::user()->name ?? ''); ?>

                                    </div>
                                    <a href="#"
                                        class="fw-semibold deep-blue fs-7"><?php echo e(Auth::user()->email ?? ''); ?></a>
                                </div>
                            </div>
                        </div>
                        <div class="separator my-2"></div>
                        <div class="menu-item px-3">
                            <a href="<?php echo e(route('profile_setting')); ?>" class="menu-link px-5">Profile</a>
                        </div>
                        <div class="menu-item px-3">
                            <a href="<?php echo e(route('business_setting')); ?>" class="menu-link px-5">Settings</a>
                        </div>
                        <div class="separator my-2"></div>
                        <div class="menu-item px-3">
                            <a href="<?php echo e(url('logout')); ?>" class="menu-link px-5 logout">Logout</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="bg-white border-top header-items">
        <div class="container">
            <ul class="nav justify-content-start py-4 gap-7">
                <?php if(auth()->check() &&
                        auth()->user()->hasAnyRole(['individual', 'business'])): ?>
                    <!-- Dashboard Menu Item -->
                    <li class="nav-item">
                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('home')): ?> active <?php endif; ?>"
                            href="<?php echo e(route('home')); ?>">
                            Dashboard
                        </a>
                    </li>

                    <!-- For Business Role, show Staff first, then Booking -->
                    <?php if(auth()->user()->hasRole('business')): ?>
                        <!-- Staff Menu Item -->
                        <li class="nav-item">
                            <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('staff_member') || request()->is('add_staff_member') || request()->is('staff-member-details')): ?> active <?php endif; ?>"
                                href="<?php echo e(route('staff_member')); ?>">
                                Staff
                            </a>
                        </li>
                    <?php endif; ?>
                    <!-- Booking Menu Item -->
                    <li class="nav-item">
                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('booking')): ?> active <?php endif; ?>"
                            href="<?php echo e(route('booking')); ?>">
                            Booking
                        </a>
                    </li>

                    <!-- Common Menu Items for Both Individual and Business -->
                    <li class="nav-item">
                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('business_services') || request()->is('add_services')): ?> active <?php endif; ?>"
                            href="<?php echo e(route('business_services')); ?>">
                            Services
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('earning')): ?> active <?php endif; ?>"
                            href="<?php echo e(route('earning')); ?>">
                            Earnings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('subscription')): ?> active <?php endif; ?>"
                            href="<?php echo e(route('subscription')); ?>">
                            Subscription
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('discount_coupon') || request()->is('add_coupon')): ?> active <?php endif; ?>"
                            href="<?php echo e(route('discount_coupon')); ?>">
                            Discount & Coupon
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('business_analytics')): ?> active <?php endif; ?>"
                            href="<?php echo e(route('business_analytics')); ?>">
                            Analytics
                        </a>
                    </li>
                <?php endif; ?>


                <!-- Admin Role Menu -->
                <?php if(auth()->check() &&
                        auth()->user()->hasRole(['admin'])): ?>
                    <div class="position-relative">


                        <div class="swiper mySwiper header-swiper">
                            <div class="swiper-wrapper">
                                <div class="swiper-slide">
                                    <li class="nav-item">
                                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('home')): ?> active <?php endif; ?>"
                                            href="<?php echo e(route('home')); ?>">
                                            Dashboard
                                        </a>
                                    </li>
                                </div>
                                <div class="swiper-slide">
                                    <li class="nav-item">
                                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('categories')): ?> active <?php endif; ?>"
                                            href="<?php echo e(route('categories.index')); ?>">
                                            Categories
                                        </a>
                                    </li>
                                </div>
                                <div class="swiper-slide">
                                    <li class="nav-item">
                                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('professionals')): ?> active <?php endif; ?>"
                                            href="<?php echo e(route('professionals')); ?>">
                                            Professionals
                                        </a>
                                    </li>
                                </div>

                                <div class="swiper-slide">
                                    <li class="nav-item">
                                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('customers')): ?> active <?php endif; ?>"
                                            href="<?php echo e(route('customers')); ?>">
                                            Customers
                                        </a>
                                    </li>
                                </div>

                                <div class="swiper-slide">
                                    <li class="nav-item">
                                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('booking')): ?> active <?php endif; ?>"
                                            href="<?php echo e(route('booking')); ?>">
                                            Bookings
                                        </a>
                                    </li>
                                </div>


                                <div class="swiper-slide">
                                    <li class="nav-item">
                                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('refund_request')): ?> active <?php endif; ?>"
                                            href="<?php echo e(route('refund_request')); ?>">
                                            Refund Requests
                                        </a>
                                    </li>
                                </div>

                                <div class="swiper-slide">
                                    <li class="nav-item">
                                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('wallet')): ?> active <?php endif; ?>"
                                            href="<?php echo e(route('wallet')); ?>">
                                            Wallet
                                        </a>
                                    </li>
                                </div>


                                <!-- <div class="swiper-slide">
                                    <li class="nav-item">
                                        <a class="nav-link fs-14 normal sora semi_bold <?php if(request()->is('business_analytics')): ?> active <?php endif; ?>" href="<?php echo e(route('business_analytics')); ?>">
                                            Analytics
                                        </a>
                                    </li>
                                </div> -->


                                <div class="swiper-slide">
                                    <li class="nav-item">
                                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('vat_mgmt')): ?> active <?php endif; ?>"
                                            href="<?php echo e(route('vat_mgmt')); ?>">
                                            VAT Management
                                        </a>
                                    </li>
                                </div>

                                <div class="swiper-slide">
                                    <li class="nav-item">
                                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('discount_coupon', 'add_coupon')): ?> active <?php endif; ?>"
                                            href="<?php echo e(route('discount_coupon')); ?>">
                                            Discount & Coupon
                                        </a>
                                    </li>
                                </div>


                                <div class="swiper-slide">
                                    <li class="nav-item">
                                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('subscription')): ?> active <?php endif; ?>"
                                            href="<?php echo e(route('subscription')); ?>">
                                            Subscription Management
                                        </a>
                                    </li>
                                </div>

                                <div class="swiper-slide">
                                    <li class="nav-item">
                                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('holidays')): ?> active <?php endif; ?>"
                                            href="<?php echo e(route('holidays')); ?>">
                                            Holidays
                                        </a>
                                    </li>
                                </div>

                                <div class="swiper-slide">
                                    <li class="nav-item">
                                        <a class="nav-link fs-14 normal sora semi_bold header-active <?php if(request()->is('certifications')): ?> active <?php endif; ?>"
                                            href="<?php echo e(route('certifications.index')); ?>">
                                            Certifications
                                        </a>
                                    </li>
                                </div>


                            </div>
                            <div class="swiper-button-next"></div>
                            <div class="swiper-button-prev"></div>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if(auth()->guard()->guest()): ?>
                    <li class="nav-item ms-auto"><a class="nav-link  deep-blue fs-14 sora semi_bold"
                            href="<?php echo e(url('register')); ?>">Become a professional →</a></li>
                <?php endif; ?>
            </ul>
        </div>
    </div>

</div>
<?php /**PATH D:\git-file\anders\resources\views/theme/layout/header.blade.php ENDPATH**/ ?>