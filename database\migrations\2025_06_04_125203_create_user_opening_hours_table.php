<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_opening_hours', function (Blueprint $table) {
            $table->id();
            $table->integer("user_id");
            $table->string("day");
            $table->enum("type", ["open", "close"]);
            $table->time("open");
            $table->time("close");
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_opening_hours');
    }
};
