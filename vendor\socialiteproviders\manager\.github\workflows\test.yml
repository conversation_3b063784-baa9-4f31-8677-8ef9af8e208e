name: Test

on:
  pull_request:
  push:
    branches: [ master ]

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      max-parallel: 15
      fail-fast: false
      matrix:
        coverage: [ 'none' ]
        php-versions: [ '8.1', '8.2', '8.3', '8.4' ]
        exclude:
          - php-versions: '8.4'
        include:
          - php-versions: '8.4'
            coverage: 'xdebug'

    name: PHP ${{ matrix.php-versions }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-versions }}
          extensions: json, mbstring, xdebug
          coverage: ${{ matrix.coverage }}

      - name: Install dependencies
        run: composer update --no-interaction --prefer-dist --no-suggest --prefer-stable

      - name: Lint composer.json
        run: composer validate --strict

      - name: Run Tests
        run: vendor/bin/phpunit -v

      - name: Upload coverage results
        if: matrix.coverage != 'none'
        uses: codecov/codecov-action@v3
