@extends('layouts.app')

@section('content')

    <div class="container-fluid login-page">
        <div class="row">
            <div class="col-md-6 px-lg-20 px-md-20 px-sm-10 d-flex flex-column align-items-center justify-content-center my-10">
                <form class="form w-100" novalidate="novalidate" method="POST" action="{{ route('login') }}">
                    @csrf
                    <div class="text-center mb-8">
                        <h1 class="text-dark fw-bolder mb-3">Sign In</h1>
                        <div class="text-gray-500 fw-semibold fs-6">Your Social Campaigns</div>
                    </div>
                    <div class="row g-3 mb-9">
                        <div class="col-md-6">
                            <a href="{ route('google.login') }}"
                                class="btn btn-flex btn-outline btn-text-gray-700 bg-state-light flex-center text-nowrap w-100">
                                <img alt="Logo" src="{{asset('website/assets')}}/images/Google_Logo.svg" class="h-15px me-3" />Sign in with Google
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="{{ route('apple.login') }}"
                                class="btn btn-flex btn-outline btn-text-gray-700 bg-state-light flex-center text-nowrap w-100">
                                <img alt="Logo" src="{{asset('website/assets')}}/images/Apple_Logo.svg"
                                class="theme-light-show h-15px me-3" />
                                <img alt="Logo" src="{{asset('website/assets')}}/images/Apple_Logo.svg"
                                class="theme-dark-show h-15px me-3" />Sign in with Apple
                            </a>
                        </div>
                    </div>
                    <div class="separator separator-content my-8">
                        <span class="w-125px text-gray-500 fw-semibold fs-7">Or with email</span>
                    </div>
                    <div class="fv-row mb-8">
                        <input id="email" type="email" placeholder="Email"
                            class="form-control  bg-transparent @error('email') is-invalid @enderror" name="email"
                            value="{{ old('email') }}" required autocomplete="email" autofocus>
                        @error('email')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                        @enderror
                    </div>
                    <div class="fv-row mb-3 position-relative">
                        <input id="password" type="password" placeholder="Password"
                            class="form-control  bg-transparent @error('password') is-invalid @enderror" name="password"
                            required autocomplete="current-password">

                        @error('password')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                        @enderror


                        <span id="toggle-password"
                            class=" btn-sm btn-icon position-absolute translate-middle end-0 pb-12 pe-2 ">
                            <i class="fa-solid fa-eye"></i>
                            <i class="fa-solid fa-eye-slash d-none"></i>
                        </span>



                    </div>
                    <div class="d-flex flex-stack flex-wrap gap-3 fs-base fw-semibold mb-8">
                        <div></div>
                        <a href="{{ route('password.request') }}" class="blue-text">Forgot Password ?</a>
                    </div>

                    <div class="d-grid mb-6">
                        <button type="submit" id="kt_sign_in_submit" class="blue-btn">
                            <span class="indicator-label">Sign In</span>
                            <span class="indicator-progress">Please wait...
                            <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                        </button>
                    </div>

                    <div class="text-gray-500 text-center fw-semibold fs-6">Not a Member yet?
                        <a href="{{ route('register') }}" class="blue-text">Sign up</a>
                    </div>
                </form>

                <div class="site_logo pt-10">
                    <a href="{{url('/')}}" class="text-center">
                        <img src="{{asset('website')}}/assets/images/logo.png" alt="icon">
                        <h4 class="blue-text pt-2"> Stylenest </h4>
                    </a>
                </div>
            </div>

            <div class="col-md-6 login-side-image">
                <img src="{{asset('website')}}/assets/images/login-banner.png" alt="icon">
            </div>
        </div>
    </div>

@endsection

@push('js')

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>

    <script>
        $(document).ready(function () {
            $('#toggle-password').on('click', function () {
                var passwordField = $('#password');
                var passwordFieldType = passwordField.attr('type');

                if (passwordFieldType === 'password') {
                    passwordField.attr('type', 'text');
                    $(this).find('.fa-eye-slash').removeClass('d-none');
                    $(this).find('.fa-eye').addClass('d-none');
                } else {
                    passwordField.attr('type', 'password');
                    $(this).find('.fa-eye').removeClass('d-none');
                    $(this).find('.fa-eye-slash').addClass('d-none');
                }
            });

        });
    </script>

@endpush