<p align="center"><img width="337" height="66" src="/art/logo.svg" alt="Logo Laravel Socialite"></p>

<p align="center">
<a href="https://github.com/laravel/socialite/actions"><img src="https://github.com/laravel/socialite/workflows/tests/badge.svg" alt="Build Status"></a>
<a href="https://packagist.org/packages/laravel/socialite"><img src="https://img.shields.io/packagist/dt/laravel/socialite" alt="Total Downloads"></a>
<a href="https://packagist.org/packages/laravel/socialite"><img src="https://img.shields.io/packagist/v/laravel/socialite" alt="Latest Stable Version"></a>
<a href="https://packagist.org/packages/laravel/socialite"><img src="https://img.shields.io/packagist/l/laravel/socialite" alt="License"></a>
</p>

## Introduction

Laravel Socialite provides an expressive, fluent interface to OAuth authentication with Bitbucket, Facebook, GitHub, GitLab, Google, LinkedIn, Slack, Twitch, and X. It handles almost all of the boilerplate social authentication code you are dreading writing.

**We are not accepting new adapters.**

Adapters for other platforms are listed at the community driven [Socialite Providers](https://socialiteproviders.com/) website.

## Official Documentation

Documentation for Socialite can be found on the [Laravel website](https://laravel.com/docs/socialite).

## Contributing

Thank you for considering contributing to Socialite! The contribution guide can be found in the [Laravel documentation](https://laravel.com/docs/contributions).

## Code of Conduct

In order to ensure that the Laravel community is welcoming to all, please review and abide by the [Code of Conduct](https://laravel.com/docs/contributions#code-of-conduct).

## Security Vulnerabilities

Please review [our security policy](https://github.com/laravel/socialite/security/policy) on how to report security vulnerabilities.

## License

Laravel Socialite is open-sourced software licensed under the [MIT license](LICENSE.md).
