<?php
declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON>\JWT\Validation\Constraint;

use <PERSON><PERSON><PERSON><PERSON>\JWT\Token;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Validation\Constraint;
use <PERSON>cobucci\JWT\Validation\ConstraintViolation;

final class IdentifiedBy implements Constraint
{
    /** @param non-empty-string $id */
    public function __construct(private readonly string $id)
    {
    }

    public function assert(Token $token): void
    {
        if (! $token->isIdentifiedBy($this->id)) {
            throw ConstraintViolation::error(
                'The token is not identified with the expected ID',
                $this,
            );
        }
    }
}
