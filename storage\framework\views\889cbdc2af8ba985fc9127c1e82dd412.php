<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12">
                    <h4 class="sora black">My Bookings</h4>
                </div>
            </div>
        </div>
        <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard">
            <div id="kt_app_content_container" class="app-container container-fluid padding-block booking-section ">
                <div class="row">
                    <div class="col-lg-3  col-md-4 calender-box">
                        <div class="d-flex align-items-center  justify-content-end">
                            <div id="inline-calendar"></div>
                        </div>
                        <!-- Buttons below the calendar -->
                        <div class="mt-2 d-flex justify-content-end  gap-3">
                            <button id="cancel-btn" class="btn btn-secondary btn-sm">Cancel</button>
                            <button id="apply-btn" class=" button blue-button px-5 py-2">Apply</button>
                        </div>
                    </div>
                    <div class="col-lg-9 col-md-10 p-0">
                        <div class="schedule-container customer-calender ">
                            <div class="calendar-header flex-align-space-btw">
                                <div class="flex-align-space-btw">
                                    <div class="calendar-controls d-flex  gap-2 align-items-center">
                                        <div class="d-flex gap-4">
                                            <p id="today" class="m-0 fs-13 regular black">Today</p>
                                            <button id="prev-week" class="btn-prev"><i
                                                    class="fa-solid fa-chevron-left"></i></button>
                                            <button id="next-week" class="btn-next"><i
                                                    class="fa-solid fa-chevron-right"></i></button>
                                        </div>
                                        <h3 id="date-range" class="m-0 fs-16 semi-bold black">14 July 2025 - 20 July 2025
                                        </h3>
                                    </div>
                                </div>
                            </div>
                            <div id="calendar"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard">
            <div id="kt_app_content_container" class="app-container container padding-block">

                <div class="row row-gap-5">
                    <!-- <div class="col-md-12">
                                    <h4 class="sora black">My Bookings</h4>
                                </div> -->


                    <div class="col-md-12">
                        <div class="card-box">
                            <p class="fs-18 semi_bold sora">Upcoming Bookings</p>
                            <div class="row">
                                <div class="col-md-12 gap-2">
                                    <div class="card shadow-none booking_card flex-row py-2 px-3 gap-3">
                                        <!-- Card Header -->
                                        <div
                                            class="card-header p-0 d-flex justify-content-between align-items-center border-0">
                                            <div
                                                class="d-flex align-items-center flex-column calendar-icon fs-20 semi_bold black">
                                                <p class="m-0 fs-20 semi_bold sora">24</p>
                                                <p class="m-0 fs-15 mormal">Dec</p>

                                            </div>
                                        </div>
                                        <!-- Card Body -->
                                        <div class="card-body  p-0 d-flex justify-content-between align-items-center">
                                            <div>
                                                <p class="mb-0 fs-15 sora balck normal">Hair Cut</p>
                                                <p class="mb-0 fs-12 sora black normal light-black ">Tue, 20
                                                    Aug 2024
                                                    2:45pm
                                                    <span class="status booked">Booked</span>
                                                </p>
                                                <p class="mb-0 fs-12 sora black normal light-black opacity-6">Justin
                                                    Dokidis
                                                    with Gustavo Dias</p>
                                            </div>
                                            <div class="d-flex gap-5 align-items-center">
                                                <p class="fs-20 semi_bold black sora m-0">$110</p>
                                                <div class="dropdown">
                                                    <button class="drop-btn" type="button" id="dropdownMenuButtonBooking"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="bi bi-three-dots-vertical"></i>
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButtonBooking">
                                                        <li>
                                                            <a href="" data-bs-target="#rescheduleBookingModalLabel"
                                                                data-bs-toggle="modal"
                                                                class="dropdown-item  black fs-14 regular " type="button">
                                                                <i class="bi bi-calendar-event me-2"></i>
                                                                Reschedule
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a href="" data-bs-target="#cancelBookingModalLabel"
                                                                data-bs-toggle="modal"
                                                                class="dropdown-item cancel fs-14 regular" type="button">
                                                                <i class="fa-solid fa-xmark cancel-icon"></i>
                                                                Cancel
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>

                                        </div>

                                    </div>
                                </div>
                                <div class="col-md-12 gap-2">
                                    <div class="card shadow-none booking_card flex-row py-2 px-3 gap-3">
                                        <!-- Card Header -->
                                        <div
                                            class="card-header p-0 d-flex justify-content-between align-items-center border-0">
                                            <div
                                                class="d-flex align-items-center flex-column calendar-icon fs-20 semi_bold black">
                                                <p class="m-0 fs-20 semi_bold sora">24</p>
                                                <p class="m-0 fs-15 mormal">Dec</p>

                                            </div>
                                        </div>
                                        <!-- Card Body -->
                                        <div class="card-body  p-0 d-flex justify-content-between align-items-center">
                                            <div>
                                                <p class="mb-0 fs-15 sora balck normal">Hair Cut</p>
                                                <p class="mb-0 fs-12 sora black normal light-black">Tue, 20
                                                    Aug 2024
                                                    2:45pm
                                                    <span class="status cancelled">Cancelled</span>
                                                </p>
                                                <p class="mb-0 fs-12 sora black normal light-black opacity-6">Justin
                                                    Dokidis
                                                    with Gustavo Dias</p>
                                            </div>
                                            <div class="d-flex gap-5 align-items-center">
                                                <p class="fs-20 semi_bold black sora m-0">$110</p>
                                                <div class="dropdown">
                                                    <button class="drop-btn" type="button" id="dropdownMenuButton2"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="bi bi-three-dots-vertical"></i>
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton2">
                                                        <li>
                                                            <a href="" data-bs-target="#rescheduleBookingModalLabel"
                                                                data-bs-toggle="modal"
                                                                class="dropdown-item  black fs-14 regular " type="button">
                                                                <i class="bi bi-calendar-event me-2"></i>
                                                                Reschedule
                                                            </a>
                                                        </li>
                                                        <a href="" data-bs-target="#cancelBookingModalLabel"
                                                            data-bs-toggle="modal"
                                                            class="dropdown-item cancel fs-14 regular" type="button">
                                                            <i class="fa-solid fa-xmark cancel-icon"></i>
                                                            Cancel
                                                        </a>
                                                    </ul>
                                                </div>
                                            </div>

                                        </div>

                                    </div>
                                </div>
                                <div class="col-md-12 gap-2">
                                    <div class="card shadow-none booking_card flex-row py-2 px-3 gap-3">
                                        <!-- Card Header -->
                                        <div
                                            class="card-header x p-0 d-flex justify-content-between align-items-center border-0">
                                            <div
                                                class="d-flex align-items-center flex-column calendar-icon fs-20 semi_bold black">
                                                <p class="m-0 fs-20 semi_bold sora">24</p>
                                                <p class="m-0 fs-15 mormal">Dec</p>

                                            </div>
                                        </div>
                                        <!-- Card Body -->
                                        <div class="card-body  p-0 d-flex justify-content-between align-items-center">
                                            <div>
                                                <p class="mb-0 fs-15 sora balck normal">Hair Cut</p>
                                                <p class="mb-0 fs-12 sora black normal light-black service-time">Tue, 20
                                                    Aug 2024
                                                    2:45pm
                                                    <span class="status complete">Complete</span>
                                                </p>
                                                <p class="mb-0 fs-12 sora black normal light-black opacity-6">Justin
                                                    Dokidis
                                                    with Gustavo Dias</p>
                                            </div>
                                            <div class="d-flex gap-5 align-items-center">
                                                <p class="fs-20 semi_bold black sora m-0">$110</p>
                                                <div class="dropdown">
                                                    <button class="drop-btn" type="button" id="dropdownMenuButton3"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="bi bi-three-dots-vertical"></i>
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton3">
                                                        <li>
                                                            <a href="" data-bs-target="#rescheduleBookingModalLabel"
                                                                data-bs-toggle="modal"
                                                                class="dropdown-item  black fs-14 regular " type="button">
                                                                <i class="bi bi-calendar-event me-2"></i>
                                                                Reschedule
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a href="" data-bs-target="#cancelBookingModalLabel"
                                                                data-bs-toggle="modal"
                                                                class="dropdown-item cancel fs-14 regular" type="button">
                                                                <i class="fa-solid fa-xmark cancel-icon"></i>
                                                                Cancel
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>

                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                    <div class="col-lg-12">
                        <p class="fs-16 sora black semi_bold">Past Bookings</p>
                        <div class="table-container">
                            <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                                <div class="search_box">
                                    <label for="customSearchInput">
                                        <i class="fas fa-search"></i>
                                    </label>
                                    <input class="search_input search" type="text" id="customSearchInput"
                                        placeholder="Search..." />
                                </div>
                                <!-- Select with dots -->
                                <div class="dropdown search_box select-box">
                                    <button
                                        class="dropdown-toggle search_input status-dropdown-button d-flex align-items-center justify-content-start"
                                        type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <span><span class="dot"></span>
                                            All</span>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item dropdown-status" href="#" data-label="All"
                                                data-color="#4B5563"><span class="dot all"></span>
                                                All</a></li>
                                        <li><a class="dropdown-item dropdown-status" href="#" data-label="Ongoing"
                                                data-color="#F59E0B"><span class="dot ongoing"></span>
                                                Ongoing</a></li>
                                        <li><a class="dropdown-item dropdown-status" href="#" data-label="Upcoming"
                                                data-color="#3B82F6"><span class="dot upcoming"></span>
                                                Upcoming</a></li>
                                        <li><a class="dropdown-item dropdown-status" href="#" data-label="Complete"
                                                data-color="#10B981"><span class="dot completed"></span>
                                                Complete</a></li>
                                        <li><a class="dropdown-item dropdown-status" href="#" data-label="Canceled"
                                                data-color="#EF4444"><span class="dot cancelled-dot"></span>
                                                Canceled</a></li>
                                    </ul>
                                </div>
                                <!-- Date Picker -->
                                <label for="datePicker" class="date_picker">
                                    <div class="date-picker-container">
                                        <i class="bi bi-calendar-event calender-icon"></i>
                                        <input type="text" name="datePicker" class="datePicker w-200px ms-3">
                                        <i class="fa fa-chevron-down down-arrow ms-9"></i>

                                    </div>
                                </label>

                            </div>

                            <!-- 📝 List View Content -->
                            <table id="responsiveTable" class="responsiveTable display nowrap w-100">
                                <thead>
                                    <tr>
                                        <th>Booking ID</th>
                                        <th>Professional</th>
                                        <th>Service Name</th>
                                        <th>Status</th>
                                        <th>Date & Time</th>
                                        <th>Amount</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php for($b = 0; $b < 20; $b++): ?>
                                        <tr>
                                            <td data-label="Booking ID">BK-9123</td>
                                            <td data-label="Professional">Carolyn Perkins</td>
                                            <td data-label="Service Name">Hair Coloring</td>
                                            <td data-label="Status" class="status paid-status">Completed</td>
                                            <td data-label="Date & Time">Apr 10, 2025 - 2:00 PM</td>
                                            <td data-label="Amount">$17.84</td>
                                            <td data-label="Action">
                                                <div class="dropdown">
                                                    <button class="drop-btn" type="button" id="dropdownMenuButtonTable"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="bi bi-three-dots-vertical"></i>
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButtontable">
                                                        <li>
                                                            <button class="dropdown-item complete fs-14 regular " type="button">
                                                                <i class="bi bi-check-circle complete-icon"></i>
                                                                Mark as Complete
                                                            </button>
                                                        </li>
                                                        <li>
                                                            <button class="dropdown-item cancel fs-14 regular" type="button">
                                                                <i class="fa-solid fa-xmark cancel-icon"></i> Cancel
                                                            </button>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td data-label="Booking ID">BK-9123</td>
                                            <td data-label="Professional">Carolyn Perkins</td>
                                            <td data-label="Service Name">Hair Coloring</td>
                                            <td data-label="Status" class="status unpaid-status">Cancelled</td>
                                            <td data-label="Date & Time">Apr 10, 2025 - 2:00 PM</td>
                                            <td data-label="Amount">$17.84</td>
                                            <td data-label="Action">
                                                <div class="dropdown">
                                                    <button class="drop-btn" type="button" id="dropdownMenuButtonTable"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="bi bi-three-dots-vertical"></i>
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButtonTable">
                                                        <li>
                                                            <button class="dropdown-item complete fs-14 regular " type="button">
                                                                <i class="bi bi-check-circle complete-icon"></i>
                                                                Mark as Complete
                                                            </button>
                                                        </li>
                                                        <li>
                                                            <button class="dropdown-item cancel fs-14 regular" type="button">
                                                                <i class="fa-solid fa-xmark cancel-icon"></i> Cancel
                                                            </button>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td data-label="Booking ID">BK-9123</td>
                                            <td data-label="Professional">Carolyn Perkins</td>
                                            <td data-label="Service Name">Hair Coloring</td>
                                            <td data-label="Status" class="status reschedule-status">Reschedule</td>
                                            <td data-label="Date & Time">Apr 10, 2025 - 2:00 PM</td>
                                            <td data-label="Amount">$17.84</td>
                                            <td data-label="Action">
                                                <div class="dropdown">
                                                    <button class="drop-btn" type="button" id="dropdownMenuButtonTable"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="bi bi-three-dots-vertical"></i>
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButtonTable">
                                                        <li>
                                                            <button class="dropdown-item complete fs-14 regular " type="button">
                                                                <i class="bi bi-check-circle complete-icon"></i>
                                                                Mark as Complete
                                                            </button>
                                                        </li>
                                                        <li>
                                                            <button class="dropdown-item cancel fs-14 regular" type="button">
                                                                <i class="fa-solid fa-xmark cancel-icon"></i> Cancel
                                                            </button>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td data-label="Booking ID">BK-9123</td>
                                            <td data-label="Professional">Carolyn Perkins</td>
                                            <td data-label="Service Name">Hair Coloring</td>
                                            <td data-label="Status" class="status pending-status">Pending</td>
                                            <td data-label="Date & Time">Apr 10, 2025 - 2:00 PM</td>
                                            <td data-label="Amount">$17.84</td>
                                            <td data-label="Action">
                                                <div class="dropdown">
                                                    <button class="drop-btn" type="button" id="dropdownMenuButtonTable"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="bi bi-three-dots-vertical"></i>
                                                    </button>
                                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButtonTable">
                                                        <li>
                                                            <button class="dropdown-item complete fs-14 regular " type="button">
                                                                <i class="bi bi-check-circle complete-icon"></i>
                                                                Mark as Complete
                                                            </button>
                                                        </li>
                                                        <li>
                                                            <button class="dropdown-item cancel fs-14 regular" type="button">
                                                                <i class="fa-solid fa-xmark cancel-icon"></i> Cancel
                                                            </button>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endfor; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <?php echo $__env->make('dashboard.templates.modal.cancel-booking-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php echo $__env->make('dashboard.templates.modal.rescheduled-booking-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
    <?php $__env->startPush('js'); ?>
    <?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\git-file\anders\resources\views/dashboard/customer/customer-booking.blade.php ENDPATH**/ ?>