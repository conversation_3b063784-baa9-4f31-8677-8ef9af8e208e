@extends('layouts.app')


@section('content')
    <div class="container professional-acc-form">
        <div class="row justify-content-center">
            <div class="col-md-12 stepper-navigation">
                <ul id="progressbar">
                    <li id="account" class="active"></li>
                    <li id="personal"></li>
                    <li id="payment"></li>
                    <li id="confirm"></li>
                    <li id="confirm"></li>
                </ul>


                 <div class="d-flex justify-content-between">
                <i name="previous" value="ll" class="fas fa-chevron-left previous action-button-previous opacity-0"></i>
                <input type="button" name="next" class="next action-button" value="Continue" />
                <input type="submit" class="submit action-button" value="Continue" />
                <!-- <a href="{{ url('/home') }}" class="submit action-button">Submit</a> -->
                </div>
            </div>

            <div class="col-md-8 mb-2 pt-20">
                <div class=" px-0 pt-4 pb-0 mt-3 mb-3">
                    <form id="acc-form" action="{{ route('register.profiessional') }}" method="POST" id="stepperForm">
                        @csrf
                        <fieldset data-step="1" class="step" id="step-1">
                            @include('dashboard.templates.professional-acc-stepper.step1')
                        </fieldset>

                        <fieldset data-step="2" class="step" id="step-2">
                            @include('dashboard.templates.professional-acc-stepper.step2')
                        </fieldset>

                        <fieldset data-step="3" class="step" id="step-3">
                            @include('dashboard.templates.professional-acc-stepper.step3')
                        </fieldset>

                        <fieldset data-step="4" class="step" id="step-4">
                            @include('dashboard.templates.professional-acc-stepper.step4')
                        </fieldset>

                        <fieldset data-step="5" class="step" id="step-5">
                            @include('dashboard.templates.professional-acc-stepper.step5')
                        </fieldset>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/dropzone.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script src="https://ajax.microsoft.com/ajax/jquery.validate/1.7/additional-methods.js"></script>

    <script src="https://cdn.jsdelivr.net/npm/intl-tel-input@17.0.19/build/js/intlTelInput.min.js"></script>
    
    <script>
        const input = document.querySelector("#phone"); // example selector
            window.intlTelInput(input, {
            initialCountry: "auto",
            geoIpLookup: function(callback) {
            fetch("https://ipinfo.io/json?token=1e240fc8539ff6")
                .then((resp) => resp.json())
                .then((resp) => {
                const countryCode = resp && resp.country ? resp.country : "us";
                callback(countryCode);
                })
                .catch(() => callback("us")); // fallback
            },
            utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/utils.js"
        });

    </script>


<script>
    const fileInput = document.querySelector('input[type="file"]');
    const wrapper = document.querySelector('.image-input-wrapper');
    const removeBtn = document.querySelector('[data-kt-image-input-action="remove"]');
    const cancelBtn = document.querySelector('[data-kt-image-input-action="cancel"]');
    const imageInput = document.querySelector('.image-input');

    fileInput.addEventListener('change', function (e) {
        const file = e.target.files[0];

        if (file) {
            const reader = new FileReader();

            reader.onload = function (event) {
                wrapper.style.backgroundImage = `url('${event.target.result}')`;
                imageInput.classList.remove('image-input-empty');
            };

            reader.readAsDataURL(file);
        }
    });

    // Remove action
    removeBtn.addEventListener('click', function () {
        wrapper.style.backgroundImage = '';
        fileInput.value = '';
        imageInput.classList.add('image-input-empty');
        // Set hidden input for backend if needed
        document.querySelector('input[name="avatar_remove"]').value = '1';
    });

    // Optional: Cancel action (if you need to reset back to default image, add that logic)
    cancelBtn.addEventListener('click', function () {
        wrapper.style.backgroundImage = '';
        fileInput.value = '';
        imageInput.classList.add('image-input-empty');
    });
</script>



    <!-- <script>
        document.querySelector('input[type="file"]').addEventListener('change', function (e) {
            const file = e.target.files[0];
            const wrapper = document.querySelector('.image-input-wrapper');

            if (file) {
                const reader = new FileReader();

                reader.onload = function (event) {
                    wrapper.style.backgroundImage = `url('${event.target.result}')`;
                };

                reader.readAsDataURL(file);
            }
        });
    </script> -->

    <script>
        $(document).ready(function () {
            var current_fs, next_fs, previous_fs;
            var opacity;
            var current = 1;
            var steps = $("fieldset").length;

            setProgressBar(current);
            toggleSubmitButton(current); // Initial check

            $(".next").click(function () {
                current_fs = $("fieldset:visible");

                // Validate current step before proceeding
                if (validateFields(current_fs)) {
                    return false; // Stop if validation fails
                }

                next_fs = current_fs.next("fieldset");
                if (next_fs.length === 0) return;

                $("#progressbar li").eq($("fieldset").index(next_fs)).addClass("active");

                next_fs.show();
                current_fs.animate({
                    opacity: 0
                }, {
                    step: function (now) {
                        opacity = 1 - now;
                        current_fs.css({
                            'display': 'none',
                            'position': 'relative'
                        });
                        next_fs.css({
                            'opacity': opacity
                        });
                    },
                    duration: 500
                });

                if ($('.action-button-previous').hasClass('opacity-0')) {
                    $('.action-button-previous').removeClass('opacity-0');
                }

                setProgressBar(++current);
                toggleSubmitButton(current);
            });

            $(".previous").click(function () {
                current_fs = $("fieldset:visible");
                previous_fs = current_fs.prev("fieldset");

                if (previous_fs.length === 0) return;

                $("#progressbar li").eq($("fieldset").index(current_fs)).removeClass("active");

                previous_fs.show();
                current_fs.animate({
                    opacity: 0
                }, {
                    step: function (now) {
                        opacity = 1 - now;
                        current_fs.css({
                            'display': 'none',
                            'position': 'relative'
                        });
                        previous_fs.css({
                            'opacity': opacity
                        });
                    },
                    duration: 500
                });

                stepNum = current_fs.attr('data-step');
                if (stepNum == '2') {
                    $('.action-button-previous').addClass('opacity-0');
                }

                setProgressBar(--current);
                toggleSubmitButton(current);
            });

            function setProgressBar(curStep) {
                var percent = parseFloat(100 / steps) * curStep;
                percent = percent.toFixed();
                $(".progress-bar").css("width", percent + "%");
            }

            function toggleSubmitButton(currentStep) {
                if (currentStep === steps) {
                    $(".submit").show();
                    $(".next").hide();
                } else {
                    $(".submit").hide();
                    $(".next").show();
                }
            }

            $(".submit").click(function (e) {
                e.preventDefault();

                // Validate all steps before submission
                let allValid = true;
                $("fieldset").each(function () {
                    if (validateFields($(this))) {
                        allValid = false;
                        // Show the invalid step
                        $(this).show();
                        current = $(this).data('step');
                        setProgressBar(current);
                        toggleSubmitButton(current);
                        return false; // break out of the loop
                    }
                });

                if (allValid) {
                    $("#acc-form").submit();
                }
            });

            function validateFields($fieldset) {
                var hasError = false;
                $fieldset.find(
                    'input:not(.no_validate,[type="hidden"], [type="checkbox"], .select2-search__field, .next, .previous), textarea:not(.no_validate)'
                ).each(function () {
                    if (!$(this).val()) {
                        $(this).addClass("valid_error");
                        $(this).css('border', '2px solid red');
                        hasError = true;
                    } else {
                        $(this).removeClass("valid_error");
                        $(this).css('border', '0.5px solid #9b9b9b');
                    }
                });
                return hasError; // Returns true if there is an error, false otherwise
            }
        });
    </script>

    <script>
        let certIndex = 0;

        function initFileUpload($group) {
            const allowedImages = ["image/png", "image/jpeg", "image/jpg"];
            const $dropArea = $group.find(".upload-box");
            const $fileInput = $group.find("input[type='file']");
            const $previewArea = $group.find(".preview-container");

            $dropArea.off();
            $fileInput.off();

            $dropArea.on("click", function () {
                $fileInput.click();
            });

            $fileInput.on("change", function (e) {
                const file = e.target.files[0];
                if (file) handleFile(file);
            });

            $dropArea.on("dragover", function (e) {
                e.preventDefault();
                $dropArea.css("background", "#eee");
            });

            $dropArea.on("dragleave", function (e) {
                e.preventDefault();
                $dropArea.css("background", "");
            });

            $dropArea.on("drop", function (e) {
                e.preventDefault();
                $dropArea.css("background", "");
                const file = e.originalEvent.dataTransfer.files[0];
                if (file) handleFile(file);
            });

            function handleFile(file) {
                if (!allowedImages.includes(file.type)) {
                    alert("Only JPG and PNG images are allowed.");
                    return;
                }

                if (file.size > 2 * 1024 * 1024) {
                    alert("File is too large. Max size: 2MB.");
                    return;
                }

                $previewArea.empty();
                $fileInput.val("");

                const reader = new FileReader();
                reader.onload = function (e) {
                    const $preview = $(`
                                                        <div class="preview-box" style="position: relative; display: inline-block; margin: 10px;">
                                                            <img src="${e.target.result}" >
                                                            <button class="remove-preview" style="position: absolute; top: 2px; right: 2px; background: red; color: white; border: none; border-radius: 50%; width: 20px; height: 20px;">×</button>
                                                        </div>
                                                    `);

                    $preview.find(".remove-preview").on("click", function () {
                        $preview.remove();
                        $fileInput.val("");
                    });

                    $previewArea.append($preview);
                };
                reader.readAsDataURL(file);
            }
        }

        function addCertificationBlock() {
            certIndex++;
            const wrapper = document.getElementById('certifications-wrapper');

            const newBlock = `
                        <div class="gray-card my-5 file-upload-group">
                            <div class="col-md-12">
                                <label class="fieldlabels">Certification Title*</label>
                                <input type="text" name="certification_title_${certIndex}" placeholder="Enter certification title"/>
                                <label class="fieldlabels">Issued by*</label>
                                <input class="no_validate" type="text" name="companyname_${certIndex}" placeholder="Enter name"/>
                            </div>

                            <div class="col-md-6">
                                <label class="fieldlabels">Issued Date*</label>
                                <input class="no_validate" type="date" name="issued_date_${certIndex}" placeholder="Enter issued date"/>
                            </div>

                            <div class="col-md-6">
                                <label class="fieldlabels">End Date*</label>
                                <input class="no_validate" type="date" name="end_date_${certIndex}" placeholder="Enter end date"/>
                            </div>

                            <div class="col-md-12 form-border">
                                <p class="manrope fw-600 light-black">Share Certificates</p>
                                <div>
                                    <label class="upload-box fs-12 normal fw-300 Plus-Jakarta-Sans" style="cursor:pointer;">
                                        <img src="{{ asset('website/assets/images/upload.svg') }}" alt="Upload Icon">
                                        <p>Upload Certificate</p>
                                        <p class="mb-0">Maximum file size: 2 MB</p>
                                        <p>Supported format: JPG and PNG</p>
                                        <span class="add-file">
                                            <p class="upload-cert-btn fs-14 fw-600"> Upload </p>
                                        </span>
                                        <input class="no_validate" type="file" hidden>
                                    </label>
                                </div>
                                <div class="preview-container"></div>

                                <div class="exception-checkbox">
                                    <label class="cert-excep">
                                        <input class="no_validate" type="checkbox" id="exceptionToggle" name="exception_${certIndex}">
                                        <span class="checkmark">Certificate Exception</span>
                                    </label>

                                    <div class="exception-textarea">
                                        <label class="mb-2" for="w3review_${certIndex}">Reason for Exception</label>
                                        <textarea class="mb-0 no_validate" id="w3review_${certIndex}" name="w3review_${certIndex}" rows="4" cols="50"
                                        placeholder="Write reason for exception"></textarea>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-3 d-flex justify-content-between">
                                <button type="button" class=" delete-block">Delete This Block</button>

                            </div>
                        </div>
                    `;

            // Insert after the last block
            wrapper.insertAdjacentHTML('beforeend', newBlock);

            // Re-initialize upload and bind delete/add buttons for the new block
            const $newGroup = $('.file-upload-group').last();
            initFileUpload($newGroup);

            // Delete handler
            $newGroup.find('.delete-block').on('click', function () {
                $newGroup.remove();
            });

            // Add More handler inside block
            $newGroup.find('.add-more-inside').on('click', function () {
                addCertificationBlock();
            });
        }

        // On top Add More button click
        document.getElementById('addMoreBtn').addEventListener('click', addCertificationBlock);

        $(document).ready(function () {
            $('.file-upload-group').each(function () {
                initFileUpload($(this));
            });

            $(document).on("dragover drop", function (e) {
                e.preventDefault();
            });
        });
    </script>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            flatpickr(".flatpickr-time", {
                enableTime: true,
                noCalendar: true,
                dateFormat: "H:i",
                time_24hr: true
            });
        });
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Get the Select All checkbox
            const selectAllCheckbox = document.querySelector('.select-all');
            // Get all individual day checkboxes
            const dayCheckboxes = document.querySelectorAll('.day-checkbox');

            // Function to toggle time picker visibility and Flatpickr
            function toggleTimePicker(checkbox, isChecked) {
                const timePickerContainer = checkbox.closest('.time-picker-calendar').querySelector('.start-time');
                const timeInputs = timePickerContainer.querySelectorAll('.flatpickr-time');

                if (isChecked) {
                    timePickerContainer.style.display = 'flex';
                    timeInputs.forEach(input => {
                        flatpickr(input, {
                            enableTime: true,
                            noCalendar: true,
                            dateFormat: "H:i",
                            time_24hr: true
                        });
                    });
                } else {
                    timePickerContainer.style.display = 'none';
                    timeInputs.forEach(input => {
                        if (input._flatpickr) {
                            input._flatpickr.destroy();
                        }
                    });
                }
            }

            // Event listener for Select All checkbox
            selectAllCheckbox.addEventListener('change', function () {
                const isChecked = this.checked;

                // Toggle all day checkboxes and their time pickers
                dayCheckboxes.forEach(checkbox => {
                    checkbox.checked = isChecked; // Check or uncheck all
                    toggleTimePicker(checkbox, isChecked); // Show or hide time picker
                });
            });

            // Existing logic for individual checkboxes
            dayCheckboxes.forEach(checkbox => {
                // Hide time picker on page load
                const timePickerContainer = checkbox.closest('.time-picker-calendar').querySelector(
                    '.start-time');
                timePickerContainer.style.display = 'none';

                // Add event listener for individual checkbox change
                checkbox.addEventListener('change', function () {
                    toggleTimePicker(this, this.checked);
                });
            });
        });
    </script>

    <script>
        $(document).ready(function () {
            const allowedDocs = [
                "application/pdf", "application/msword",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "application/ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "application/vnd.ms-powerpoint",
                "application/vnd.openxmlformats-officedocument.presentationml.presentation",
                "application/zip"
            ];
            const allowedImages = ["image/png", "image/jpeg", "image/jpg"];

            // Generic handler for multiple dropzones
            $("[data-type]").each(function () {
                const dropArea = $(this);
                const fileInput = dropArea.find(".file-input");
                const previewContainer = dropArea.find(".preview-container");
                const type = dropArea.data("type");

                dropArea.on("click", function () {
                    fileInput.click();
                });

                fileInput.on("change", function (e) {
                    handleFiles(e.target.files, type, previewContainer);
                });

                dropArea.on("dragover", function (e) {
                    e.preventDefault();
                    dropArea.css("background", "#ddd");
                });

                dropArea.on("dragleave", function (e) {
                    e.preventDefault();
                    dropArea.css("background", "#f9f9f9");
                });

                dropArea.on("drop", function (e) {
                    e.preventDefault();
                    dropArea.css("background", "#f9f9f9");
                    handleFiles(e.originalEvent.dataTransfer.files, type, previewContainer);
                });
            });

            function handleFiles(files, type, previewContainer) {
                $.each(files, function (index, file) {
                    const fileType = file.type;
                    const fileReader = new FileReader();

                    if (type === "certificate") {
                        if (!allowedImages.includes(fileType)) {
                            alert("Only image files (PNG, JPG, JPEG) are allowed!");
                            return;
                        }

                        fileReader.onload = function (e) {
                            const previewElement = $("<div class='preview-box'></div>");
                            previewElement.append(`<img src="${e.target.result}" alt="Image Preview">`);
                            const removeBtn = $("<button class='remove-image'>X</button>");
                            removeBtn.on("click", function () {
                                previewElement.remove();
                            });
                            previewElement.append(removeBtn);
                            previewContainer.append(previewElement);
                        };

                    } else if (type === "portfolio") {
                        if (!allowedDocs.includes(fileType)) {
                            alert("Only document files (PDF, DOCX, ZIP, etc.) are allowed!");
                            return;
                        }

                        fileReader.onload = function () {
                            const fileName = file.name;
                            const fileDate = new Date().toLocaleDateString();
                            const previewElement = $("<div class='preview-box-file'></div>");

                            let icon = "<i class='fa-solid fa-file' style='color: black;'></i>";
                            if (fileType === "application/pdf") icon =
                                "<i class='fa-solid fa-file-pdf' style='color: #d70e0ef2;'></i>";
                            if (fileType.includes("word")) icon =
                                "<i class='fa-solid fa-file-word' style='color: #1d517f;'></i>";
                            if (fileType.includes("excel")) icon =
                                "<i class='fa-solid fa-file-excel' style='color: #137b13;'></i>";
                            if (fileType.includes("powerpoint")) icon =
                                "<i class='fa-solid fa-file-powerpoint' style='color: orange;'></i>";
                            if (fileType === "application/zip") icon =
                                "<i class='fa-solid fa-file-zipper' style='color: brown;'></i>";

                            previewElement.append(icon);
                            previewElement.append(`<div class="file-name d-flex flex-column">
                                                                                                                                <p class="dark-charcoal manrope fs-14 fw-500">${fileName}</p>
                                                                                                                                <p class="dark-charcoal manrope fs-14 normal">${fileDate}</p>
                                                                                                                            </div>`);
                            const removeBtn = $("<button class='remove-file'>X</button>");
                            removeBtn.on("click", function () {
                                previewElement.remove();
                            });
                            previewElement.append(removeBtn);
                            previewContainer.append(previewElement);
                        };
                    }

                    fileReader.readAsDataURL(file);
                });
            }
        });
    </script>

    <script>
        var croppedProfileImage = null;
        var croppedProfileImageName = null;

        const profilePreviewNode = document.querySelector("#profile_dzTemplate");
        const profilePreviewTemplate = profilePreviewNode.parentNode.innerHTML;
        profilePreviewNode.remove();

        cropDrop = new Dropzone("#profileImg", {
            url: 'ajaxtest.php',
            maxFiles: 1,
            acceptedFiles: ".png,.jpg,.jpeg",
            thumbnailWidth: 860,
            thumbnailHeight: 332,
            previewTemplate: profilePreviewTemplate,
            previewsContainer: "#profile_dzPreviews",

            init: function () {
                this.on("addedfile", file => {
                    if (!file.type.match(/image.*/)) {
                        this.removeFile(file);
                        alert("Only image files are allowed.");
                    }
                });

                this.on("removedfile", () => {
                    croppedProfileImage = null;
                    croppedProfileImageName = null;
                    $('#profileImg').fadeIn(100);

                });
            },

            transformFile: function (file, done) {
                const modal = document.getElementById("cropmodal");
                const template = document.getElementById("modaltpl").content.cloneNode(true);
                const modalBody = template.querySelector(".modal-body");
                const modalFooter = template.querySelector(".modal-footer");
                const imgContainer = template.querySelector(".img-container");

                const image = new Image();
                image.src = URL.createObjectURL(file);
                imgContainer.innerHTML = "";
                imgContainer.appendChild(image);

                let cropper;
                const confirmBtn = document.createElement("button");
                confirmBtn.type = "button";
                confirmBtn.className = "blue-btn w-200px py-3";
                confirmBtn.textContent = "Crop & Upload";
                confirmBtn.addEventListener("click", function () {
                    const canvas = cropper.getCroppedCanvas({
                        width: 2520,
                        height: 1080
                    });
                    canvas.toBlob(blob => {
                        cropDrop.createThumbnail(blob, cropDrop.options.thumbnailWidth, cropDrop.options.thumbnailHeight, cropDrop.options.thumbnailMethod, false, function (dataURL) {
                            cropDrop.emit("thumbnail", file, dataURL);
                            done(blob); // Pass blob to dropzone
                            croppedProfileImage = blob;
                            croppedProfileImageName = file.name;
                            $('#cropmodal').modal('hide');
                            cropper.destroy();
                            modal.innerHTML = '';
                        });
                    });
                });

                modalFooter.appendChild(confirmBtn);
                modal.appendChild(template);

                $('#cropmodal').modal('show');
                $('#cropmodal').on('shown.bs.modal', function () {
                    cropper = new Cropper(image, {
                        aspectRatio: 21 / 9,
                        viewMode: 1
                    });
                });

                $('#cropmodal').on('hidden.bs.modal', function () {
                    if (cropper) {
                        cropper.destroy();
                    }
                    modal.innerHTML = '';
                    $('#profileImg').fadeOut(100);
                });
            }
        });
    </script>

    <script>
    document.addEventListener('DOMContentLoaded', function () {
        const selectAllCheckbox = document.querySelector('.select-all');

        // Delegate for checkbox and delete interactions
        document.addEventListener('change', function (e) {
            if (e.target.classList.contains('day-checkbox')) {
                const checkbox = e.target;
                const timePickerContainer = checkbox.closest('.time-picker-calendar').querySelector('.start-time');

                if (checkbox.checked) {
                    timePickerContainer.style.display = 'flex';
                    timePickerContainer.querySelectorAll('.flatpickr-time').forEach(input => {
                        flatpickr(input, {
                            enableTime: true,
                            noCalendar: true,
                            dateFormat: "H:i",
                            time_24hr: true
                        });
                    });
                } else {
                    timePickerContainer.style.display = 'none';
                    timePickerContainer.querySelectorAll('.flatpickr-time').forEach(input => {
                        if (input._flatpickr) input._flatpickr.destroy();
                    });
                }
            }

            if (e.target.classList.contains('select-all')) {
                const isChecked = e.target.checked;
                document.querySelectorAll('.day-checkbox').forEach(cb => {
                    cb.checked = isChecked;
                    cb.dispatchEvent(new Event('change'));
                });
            }
        });

        // Save custom holiday
        document.getElementById('saveCustomHoliday').addEventListener('click', function () {
            const name = document.getElementById('customHolidayName').value.trim();
            const date = document.getElementById('customHolidayDate').value.trim();

            if (name && date) {
                const wrapper = document.createElement('div');
                wrapper.classList.add('time-picker-calendar');
                wrapper.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <label class="days">
                            <input type="checkbox" class="day-checkbox">
                            <span class="checkmark">${name}</span>
                        </label>
                        <p>${date}</p>  
                    </div>
                    <button class="delete-holiday btn btn-sm btn-danger mb-8" >Delete</button>

                    <div class="start-time" style="display: none;">
                        <div class="d-flex gap-10">
                            <input type="text" class="flatpickr-time no_validate" placeholder="Select Time">
                            <p> - </p>
                            <input type="text" class="flatpickr-time no_validate" placeholder="Select Time">
                        </div>
                    </div>`;

                document.querySelector('.add-custom-holiday-btn').before(wrapper);

                const checkbox = wrapper.querySelector('.day-checkbox');
                const timePickerContainer = wrapper.querySelector('.start-time');

                // Bind checkbox behavior
                checkbox.addEventListener('change', function () {
                    if (this.checked) {
                        timePickerContainer.style.display = 'flex';
                        timePickerContainer.querySelectorAll('.flatpickr-time').forEach(input => {
                            flatpickr(input, {
                                enableTime: true,
                                noCalendar: true,
                                dateFormat: "H:i",
                                time_24hr: true
                            });
                        });
                    } else {
                        timePickerContainer.style.display = 'none';
                        timePickerContainer.querySelectorAll('.flatpickr-time').forEach(input => {
                            if (input._flatpickr) input._flatpickr.destroy();
                        });
                    }
                });

                // Auto-check and trigger change
                checkbox.checked = true;
                checkbox.dispatchEvent(new Event('change'));

                // Clear modal and hide
                document.getElementById('customHolidayName').value = '';
                document.getElementById('customHolidayDate').value = '';
                document.getElementById('customHolidayModal').style.display = 'none';
            } else {
                alert('Please fill in both holiday name and date.');
            }
        });

        // Delete handler
        document.addEventListener('click', function (e) {
            if (e.target.classList.contains('delete-holiday')) {
                const wrapper = e.target.closest('.time-picker-calendar');
                if (wrapper) wrapper.remove();
            }
        });

        // Modal controls
        document.querySelector('.add-custom-holiday-btn').addEventListener('click', function () {
            document.getElementById('customHolidayModal').style.display = 'flex';
        });
        document.querySelector('.modal .close').addEventListener('click', function () {
            document.getElementById('customHolidayModal').style.display = 'none';
        });

        // Initialize flatpickr date input
        flatpickr('#customHolidayDate', {
            dateFormat: "F j"
        });

        // Auto-check the first holiday checkbox
        const firstCheckbox = document.querySelector('.day-checkbox');
        if (firstCheckbox) {
            firstCheckbox.checked = true;
            firstCheckbox.dispatchEvent(new Event('change'));
        }
    });
    </script>




@endpush