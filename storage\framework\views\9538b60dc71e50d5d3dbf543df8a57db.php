<div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard business-home add-service">
    <div id="kt_app_content_container" class="app-container container-fluid">
        <section class="business-home-sec padding-block">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="semi_bold">Welcome, <PERSON></h6>
                        <p class="fs-14 normal">Lorem ipsum dolor sit amet consectetur. </p>
                    </div>
                    <div class=" col-md-6">
                        <div class="d-flex justify-content-end align-items-center gap-5">
                            <!-- export btn -->
                            <div class="search_box d-block">
                                <button class="search_input fs-14 normal link-gray ">
                                    Export All Data <i class="ms-1 bi bi-file-arrow-down"></i>
                                </button>
                            </div>
                            <!-- category -->
                            <div class="search_box select-box">
                                <select class="search_input">
                                    <option value="select">Select</option>
                                    <option value="Weekly"> Weekly</option>
                                    <option value="Monthly">Monthly</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row row-gap-5 mb-10 card-wrapper">
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="javascript:void(0)">
                            <div class=" card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-light-blue">
                                        <?php echo $__env->make('svg.calender', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                    </div>
                                </div>
                                <div class="card-body w-50">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        Total Bookings
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                        data-kt-countup-value="54296" data-kt-countup-prefix="$">
                                    </p>
                                </div>
                                <div class="card-footer w-50">
                                    <div class="fs-12 w-700 green green-box">
                                        <i class="fa-solid fa-arrow-up analytics-green-arrow "></i>
                                        17.2%
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="javascript:void(0)">
                            <div class=" card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-light-green">
                                        <?php echo $__env->make('svg.customer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                    </div>
                                </div>
                                <div class="card-body w-50 ">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        Total Customers
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                        data-kt-countup-value="3059" data-kt-countup-prefix="$">
                                    </p>
                                </div>
                                <div class="card-footer w-50">
                                    <div class="fs-12 w-700 green green-box">
                                        <i class="fa-solid fa-arrow-up analytics-green-arrow"></i>
                                        32.7%
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="javascript:void(0)">
                            <div class=" card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-purple">
                                        <?php echo $__env->make('svg.dollar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                    </div>
                                </div>
                                <div class="card-body w-50 ">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        Total Professionals
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                        data-kt-countup-value="1252" data-kt-countup-prefix="$">
                                    </p>
                                </div>
                                <div class="card-footer w-50">
                                    <div class="fs-12 w-700 green green-box">
                                        <i class="fa-solid fa-arrow-up analytics-green-arrow"></i>
                                        32.7%
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="javascript:void(0)">
                            <div class=" card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-orange">
                                        <?php echo $__env->make('svg.professional', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                    </div>
                                </div>
                                <div class="card-body w-50 ">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        Total Revenue
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                        data-kt-countup-value="13055">
                                    </p>
                                </div>
                                <div class="card-footer w-50">
                                    <div class="fs-12 w-700 green green-box">
                                        <i class="fa-solid fa-arrow-up analytics-green-arrow"></i>
                                        32.7%
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-7">
                        <div class="  card-box">
                            <p class="black sora semi_bold">Revenue</p>
                            <canvas id="popularServicesChart"></canvas>
                        </div>
                    </div>
                    <div class="col-lg-5">
                        <div class="  card-box">
                            <p class="black sora semi_bold">Top Category Purchases</p>
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="chart-container-booking">
                                    <canvas id="bookingChart" class="bookingchart"></canvas>
                                </div>
                                <div id="chartLegend"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row row-gap-5 pt-5">
                    <div class="col-md-12 d-flex justify-content-between align-items-center">
                        <h6 class="sora black">Professionals & Customers</h6>
                    </div>
                    <div class="col-lg-12">
                        <ul class="nav nav-pills mb-10" id="pills-tab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active business-services" id="professionals-tab"
                                    data-bs-toggle="pill" data-bs-target="#pills-professionals" type="button" role="tab"
                                    aria-controls="pills-professionals" aria-selected="true">
                                    Professionals
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link business-services" id="Customers-tab" data-bs-toggle="pill"
                                    data-bs-target="#pills-customers" type="button" role="tab"
                                    aria-controls="pills-customers" aria-selected="false">
                                    Customers
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content" id="pills-tabContent">
                            <div class="tab-pane fade show active" id="pills-professionals" role="tabpanel"
                                aria-labelledby="professionals-tab" tabindex="0">
                                <div class="table-container">
                                    <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                                        <!-- search -->
                                        <div class="search_box">
                                            <label for="customSearchInput">
                                                <i class="fas fa-search"></i>
                                            </label>
                                            <input class="search_input search" type="text" id="customSearchInput"
                                                placeholder="Search..." />
                                        </div>
                                        <!-- Select with dots -->
                                        <div class="dropdown search_box select-box">
                                            <button
                                                class="dropdown-toggle search_input status-dropdown-button d-flex align-items-center justify-content-start"
                                                type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                <span><span class="dot"
                                                        style="background:#4B5563; width: 8px; height: 8px; border-radius: 50%; display:inline-block;"></span>
                                                    All</span>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item dropdown-status" href="#" data-label="All"
                                                        data-color="#4B5563"><span class="dot all"></span>All</a></li>
                                                <li><a class="dropdown-item dropdown-status" href="#"
                                                        data-label="Ongoing" data-color="#F59E0B"><span
                                                            class="dot ongoing"></span>Ongoing</a></li>
                                                <li><a class="dropdown-item dropdown-status" href="#"
                                                        data-label="Upcoming" data-color="#3B82F6"><span
                                                            class="dot upcoming"></span>Upcoming</a></li>
                                                <li><a class="dropdown-item dropdown-status" href="#"
                                                        data-label="Complete" data-color="#10B981"><span
                                                            class="dot completed"></span>Complete</a></li>
                                                <li><a class="dropdown-item dropdown-status" href="#"
                                                        data-label="Canceled" data-color="#EF4444"><span
                                                            class="dot cancelled-dot"></span> Canceled</a></li>
                                            </ul>
                                        </div>
                                        <!-- category -->
                                        <div class="search_box select-box">
                                            <select class="search_input">
                                                <option value="Category">Category</option>
                                                <option value="all">All</option>
                                                <option value=" Group"> Group</option>
                                                <option value="Individual">Individual</option>
                                            </select>
                                        </div>
                                        <!-- Date Picker -->
                                        <label for="datePicker" class="date_picker">
                                            <div class="date-picker-container">
                                                <i class="bi bi-calendar-event calender-icon"></i>
                                                <input type="text" name="datePicker" class="datePicker ms-3 w-200px">
                                                <i class="fa fa-chevron-down down-arrow ms-9"></i>
                                            </div>
                                        </label>
                                        <!-- export btn --> 
                                        <div class="search_box d-block ms-auto">
                                            <button" class="search_input fs-14 normal link-gray ">
                                                Export Data <i class="ms-1 bi bi-file-arrow-down file-icon"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <table id="responsiveTable" class="responsiveTable display" style="width: 100%">
                                        <thead>
                                            <tr>
                                                <th></th>
                                                <th>Email Address</th>
                                                <th>Category</th>
                                                <th>service category</th>
                                                <th>Action</th>
                                                <th></th>
                                                <th></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php for($b = 0; $b < 3; $b++): ?>
                                                <tr>
                                                    <td>
                                                        <div
                                                            class="card flex-row shadow-none p-0 gap-3 align-items-center">
                                                            <div class="card-header p-0 border-0 align-items-start">
                                                                <img src="<?php echo e(asset('website')); ?>/assets/images/professional-name.png"
                                                                    class="h-80px w-80px rounded-3 object-fit-contain"
                                                                    alt="card-image" />
                                                            </div>
                                                            <div class="card-body p-0 ">
                                                                <p class="fs-16 regular black m-0 pb-5">Ruben Vaccaro</p>
                                                                <p class="fs-14 semi_bold sora black m-0"><i
                                                                        class="fa-solid fa-star"></i> 5.0 </p>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td data-label="EMAIL ADDRESS"><EMAIL></td>
                                                    <td data-label="SERVICE CATEGORY">Hair Stylist</td>

                                                    <td data-label="STATUS" class="professional-status status paid-status">
                                                        Active</td>

                                                    <td data-label="JOINED DATE">Jan 5,2024</td>
                                                    <td data-label="TOTAL BOOKINGS">120</td>

                                                    <td data-label="">
                                                        <div class="dropdown">
                                                            <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                                data-bs-toggle="dropdown" aria-expanded="false">
                                                                <i class="bi bi-three-dots-vertical"></i>
                                                            </button>
                                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                                <li>
                                                                    <button class="dropdown-item complete fs-14 regular "
                                                                        type="button">
                                                                        <i class="bi bi-check-circle complete-icon"></i>
                                                                        Mark as Complete
                                                                    </button>
                                                                </li>
                                                                <li>
                                                                    <button class="dropdown-item cancel fs-14 regular"
                                                                        type="button">
                                                                        <i class="fa-solid fa-xmark cancel-icon"></i> Cancel
                                                                    </button>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <div
                                                            class="card  flex-row shadow-none p-0 gap-3 align-items-center">
                                                            <div class="card-header p-0 border-0 align-items-start">
                                                                <img src="<?php echo e(asset('website')); ?>/assets/images/professional-name.png"
                                                                    class="h-80px w-80px rounded-3 object-fit-contain"
                                                                    alt="card-image" />
                                                            </div>
                                                            <div class="card-body p-0 ">
                                                                <p class="fs-16 regular black m-0 pb-5">Ruben Vaccaro</p>
                                                                <p class="fs-14 semi_bold sora black m-0"><i
                                                                        class="fa-solid fa-star"></i> 5.0 </p>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td data-label="EMAIL ADDRESS"><EMAIL></td>
                                                    <td data-label="SERVICE CATEGORY">Hair Stylist</td>

                                                    <td data-label="STATUS"
                                                        class="professional-status status unpaid-status">Inactive</td>
                                                    <td data-label="JOINED DATE">Feb 5,2024</td>
                                                    <td data-label="TOTAL BOOKINGS">85</td>

                                                    <td data-label="">
                                                        <div class="dropdown">
                                                            <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                                data-bs-toggle="dropdown" aria-expanded="false">
                                                                <i class="bi bi-three-dots-vertical"></i>
                                                            </button>
                                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                                <li>
                                                                    <button class="dropdown-item complete fs-14 regular "
                                                                        type="button">
                                                                        <i class="bi bi-check-circle complete-icon"></i>
                                                                        Mark as Complete
                                                                    </button>
                                                                </li>
                                                                <li>
                                                                    <button class="dropdown-item cancel fs-14 regular"
                                                                        type="button">
                                                                        <i class="fa-solid fa-xmark cancel-icon"></i> Cancel
                                                                    </button>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endfor; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="pills-customers" role="tabpanel"
                                aria-labelledby="Customers-tab" tabindex="1">
                                <div class="table-container">
                                    <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                                        <div class="search_box">
                                            <label for="customSearchInput">
                                                <i class="fas fa-search"></i>
                                            </label>
                                            <input class="search_input search" type="text" id="customSearchInput"
                                                placeholder="Search..." />
                                        </div>
                                        <!-- Select with dots -->
                                        <div class="dropdown search_box select-box">
                                            <button
                                                class="dropdown-toggle search_input status-dropdown-button d-flex align-items-center justify-content-start"
                                                type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                <span><span class="dot"
                                                        style="background:#4B5563; width: 8px; height: 8px; border-radius: 50%; display:inline-block;"></span>
                                                    All</span>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item dropdown-status" href="#" data-label="All"
                                                        data-color="#4B5563"><span class="dot all"></span>
                                                        All</a></li>
                                                <li><a class="dropdown-item dropdown-status" href="#"
                                                        data-label="Ongoing" data-color="#F59E0B"><span
                                                            class="dot ongoing"></span>
                                                        Ongoing</a></li>
                                                <li><a class="dropdown-item dropdown-status" href="#"
                                                        data-label="Upcoming" data-color="#3B82F6"><span
                                                            class="dot upcoming"></span>
                                                        Upcoming</a></li>
                                                <li><a class="dropdown-item dropdown-status" href="#"
                                                        data-label="Complete" data-color="#10B981"><span
                                                            class="dot completed"></span>
                                                        Complete</a></li>
                                                <li><a class="dropdown-item dropdown-status" href="#"
                                                        data-label="Canceled" data-color="#EF4444"><span
                                                            class="dot cancelled-dot"></span>
                                                        Canceled</a></li>
                                            </ul>
                                        </div>
                                        <!-- category -->
                                        <div class="search_box select-box">
                                            <select class="search_input">
                                                <option value="Category">Category</option>
                                                <option value="all">All</option>
                                                <option value=" Group"> Group</option>
                                                <option value="Individual">Individual</option>
                                            </select>
                                        </div>
                                        <!-- Date Picker -->
                                        <label for="datePicker" class="date_picker">
                                            <div class="date-picker-container">
                                                <i class="bi bi-calendar-event calender-icon"></i>
                                                <input type="text" name="datePicker" class="datePicker w-200px ms-3">
                                                <i class="fa fa-chevron-down down-arrow ms-9"></i>
                                            </div>
                                        </label>
                                        <!-- export btn -->
                                        <div class="search_box d-block ms-auto">
                                            <button class="search_input fs-14 normal link-gray ">
                                                Export Data <i class="ms-1 bi bi-file-arrow-down file-icon"></i>
                                            </button>
                                        </div>

                                    </div>
                                    <table id="responsiveTable" class="responsiveTable display" style="width: 100%">
                                        <thead>
                                            <tr>
                                                <th></th>
                                                <th>Email Address</th>
                                                <th>Category</th>
                                                <th>Bookings</th>
                                                <th>Action</th>
                                                <th></th>
                                                <th></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php for($b = 0; $b < 20; $b++): ?>
                                                <tr>
                                                    <td>
                                                        <div
                                                            class="card  flex-row shadow-none p-0 gap-3 align-items-center">
                                                            <div class="card-header p-0 border-0 align-items-start">
                                                                <img src="<?php echo e(asset('website')); ?>/assets/images/professional-name.png"
                                                                    class="h-80px w-80px rounded-3 object-fit-contain"
                                                                    alt="card-image" />
                                                            </div>
                                                            <div class="card-body p-0 ">
                                                                <p class="fs-16 regular black m-0 pb-5">Ruben Vaccaro</p>
                                                                <p class="fs-14 semi_bold sora black m-0"><i
                                                                        class="fa-solid fa-star"></i> 5.0 </p>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td data-label="EMAIL ADDRESS"><EMAIL></td>
                                                    <td data-label="SUBSCRIPTION">Individual</td>
                                                    <td data-label="STATUS" class="professional-status status paid-status">
                                                        Active</td>
                                                    <td data-label="JOINED DATE">Jan 5,2024</td>
                                                    <td data-label="TOTAL BOOKINGS">120</td>
                                                    <td data-label="">
                                                        <div class="dropdown">
                                                            <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                                data-bs-toggle="dropdown" aria-expanded="false">
                                                                <i class="bi bi-three-dots-vertical"></i>
                                                            </button>
                                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                                <li>
                                                                    <button class="dropdown-item complete fs-14 regular"
                                                                        type="button">
                                                                        <i class="bi bi-check-circle complete-icon"></i>
                                                                        Mark as Complete
                                                                    </button>
                                                                </li>
                                                                <li>
                                                                    <button class="dropdown-item cancel fs-14 regular"
                                                                        type="button">
                                                                        <i class="fa-solid fa-xmark cancel-icon"></i> Cancel
                                                                    </button>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <div class="card flex-row shadow-none p-0 gap-3 align-items-center">
                                                            <div class="card-header p-0 border-0 align-items-start">
                                                                <img src="<?php echo e(asset('website')); ?>/assets/images/professional-name.png"
                                                                    class="h-80px w-80px rounded-3 object-fit-contain"
                                                                    alt="card-image" />
                                                            </div>
                                                            <div class="card-body p-0">
                                                                <p class="fs-16 regular black m-0 pb-5">Ruben Vaccaro</p>
                                                                <p class="fs-14 semi_bold sora black m-0"><i
                                                                        class="fa-solid fa-star"></i> 5.0 </p>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td data-label="EMAIL ADDRESS"><EMAIL></td>
                                                    <td data-label="SUBSCRIPTION">Business</td>

                                                    <td data-label="STATUS"
                                                        class="professional-status status unpaid-status">Inactive</td>
                                                    <td data-label="JOINED DATE">Feb 5,2024</td>
                                                    <td data-label="TOTAL BOOKINGS">85</td>

                                                    <td data-label="">
                                                        <div class="dropdown">
                                                            <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                                data-bs-toggle="dropdown" aria-expanded="false">
                                                                <i class="bi bi-three-dots-vertical"></i>
                                                            </button>
                                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                                <li>
                                                                    <button class="dropdown-item complete fs-14 regular "
                                                                        type="button">
                                                                        <i class="bi bi-check-circle complete-icon"></i>
                                                                        Mark as Complete
                                                                    </button>
                                                                </li>
                                                                <li>
                                                                    <button class="dropdown-item cancel fs-14 regular"
                                                                        type="button">
                                                                        <i class="fa-solid fa-xmark cancel-icon"></i> Cancel
                                                                    </button>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endfor; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>



<?php $__env->startPush('js'); ?>
    <!-- donut-chart -->
    <script>
        var ctx4 = document.getElementById('bookingChart');
        var blue = '#62B2FD';
        var green = '#9BDFC4';
        var pink = '#F99BAB';
        var lightGray = '#F3F4F6';


        const config4 = {
            type: 'doughnut',
            data: {
                labels: ['Personal Trainers', 'Makeup Artists', 'Hair Stylists'],
                datasets: [{
                    label: 'Booking Analytics',
                    data: [50, 25, 25],
                    backgroundColor: [blue, pink, green],
                    borderColor: '#fff',
                    borderWidth: 3,
                    hoverOffset: 0,
                }]
            },
            options: {
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        callbacks: {
                            title: (context) => context[0].label,
                            label: (context) => `${context.raw} Bookings`
                        }
                    }
                },

                cutout: '75%',
                responsive: true,
                maintainAspectRatio: false
            }
        };

        var myChart4 = new Chart(ctx4, config4);

        // Custom Legend (skip light grey entry)
        function createCustomLegend(chart) {
            const legendContainer = document.getElementById('chartLegend');
            const data = chart.data;
            let legendHTML = '';

            data.labels.forEach((label, index) => {
                if (label !== '') { // Skip light gray segments
                    const color = data.datasets[0].backgroundColor[index];

                    legendHTML += `
                                            <div style="display: flex; align-items: center; margin-bottom: 10px;">
                                                <span style="width: 10px; height: 10px; background-color: ${color}; border-radius: 50%; display: inline-block; margin-right: 10px;"></span>
                                                <span style="font-size: 12px; color: #6B7280; margin-right: 6px;">${data.datasets[0].data[index]}%</span>
                                                <span style="font-size: 12px; font-weight: 500; color: #111827;">
                                                    ${label}
                                                </span>
                                            </div>
                                        `;
                }
            });

            legendContainer.innerHTML = legendHTML;
        }

        createCustomLegend(myChart4);



    </script>

    <!-- revenue-chart -->
    <script>
        const ctx = document.getElementById('popularServicesChart').getContext('2d');

        const data = {
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            datasets: [{
                label: 'By Subscription',
                data: [140, 210, 160, 240, 300, 300, 350],
                borderColor: '#3B82F6',
                backgroundColor: function (context) {
                    const chart = context.chart;
                    const { ctx, chartArea } = chart;
                    if (!chartArea) return null;

                    const gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);
                    gradient.addColorStop(0, 'rgba(59, 130, 246, 0.00)');
                    gradient.addColorStop(1, 'rgba(59, 130, 246, 0.15)');
                    return gradient;
                },
                fill: true,
                tension: 0.5,
                pointBackgroundColor: 'blue',
                pointBorderColor: 'transparent',
                pointRadius: 1,
                pointHoverRadius: 7,
            },
            {
                label: 'By Customers',
                data: [100, 150, 220, 200, 220, 180, 240],
                borderColor: '#0BC688',
                backgroundColor: 'rgba(0, 255, 0, 0.1)',
                fill: false,
                tension: 0.5,
                pointBackgroundColor: '#0BC688',
                pointBorderColor: 'transparent',
                pointRadius: 0,
                pointHoverRadius: 7,
            },
            {
                label: 'By Professionals',
                data: [110, 170, 260, 250, 260, 270, 250],
                borderColor: '#F1962D',
                backgroundColor: 'rgba(255, 165, 0, 0.1)',
                fill: false,
                tension: 0.5,
                pointBackgroundColor: '#F1962D',
                pointBorderColor: 'transparent',
                pointRadius: 0,
                pointHoverRadius: 7,
            }
            ]
        };

        const config = {
            type: 'line',
            data: data,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: true,
                        position: 'bottom',
                        align: 'start',
                        labels: {
                            usePointStyle: true,
                            pointStyle: 'dot',
                            padding: 20,
                            boxWidth: 5,
                            boxHeight: 5,
                        }
                    },
                    tooltip: {
                        enabled: false, // Disable the default tooltip
                        external: function (context) {
                            // Tooltip Element
                            let tooltipEl = document.getElementById('chartjs-tooltip');

                            // Create element on first render
                            if (!tooltipEl) {
                                tooltipEl = document.createElement('div');
                                tooltipEl.id = 'chartjs-tooltip';
                                tooltipEl.style.background = 'white';
                                tooltipEl.style.borderRadius = '8px';
                                tooltipEl.style.boxShadow = '0 4px 10px rgba(0, 0, 0, 0.1)';
                                tooltipEl.style.color = '#111827';
                                tooltipEl.style.opacity = 1;
                                tooltipEl.style.pointerEvents = 'none';
                                tooltipEl.style.position = 'absolute';
                                tooltipEl.style.transform = 'translate(-50%, -100%)';
                                tooltipEl.style.transition = 'all .1s ease';
                                tooltipEl.style.padding = '8px 12px';
                                tooltipEl.style.fontFamily = 'sans-serif';
                                tooltipEl.style.fontSize = '14px';
                                document.body.appendChild(tooltipEl);
                            }

                            // Hide if no tooltip
                            const tooltipModel = context.tooltip;
                            if (tooltipModel.opacity === 0) {
                                tooltipEl.style.opacity = 0;
                                return;
                            }

                            const dataPoint = tooltipModel.dataPoints[0];
                            const label = dataPoint.dataset.label;
                            const value = dataPoint.raw;
                            const color = dataPoint.element.options.backgroundColor;

                            // Set inner HTML
                            tooltipEl.innerHTML = `
                                                                        <div style="display: flex; align-items: center; gap: 8px;">
                                                                            <span style="width: 10px; height: 10px; border-radius: 50%; background:${color}; display:inline-block;"></span>
                                                                            <div>
                                                                                <div style="font-weight: 600; font-size: 16px;">$${value.toLocaleString()}</div>
                                                                                <div style="font-size: 13px; color: #6B7280;">${label}</div>
                                                                            </div>
                                                                        </div>
                                                                    `;

                            // Positioning
                            const { offsetLeft: positionX, offsetTop: positionY } = context.chart.canvas;
                            tooltipEl.style.left = positionX + tooltipModel.caretX + 'px';
                            tooltipEl.style.top = positionY + tooltipModel.caretY + 'px';
                            tooltipEl.style.opacity = 1;
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 12,
                                weight: '400',
                                opacity: 0.6,
                            },
                            color: '#363636'
                        }
                    },
                    y: {
                        grid: {
                            display: true
                        },
                        beginAtZero: true,
                        min: 50,
                        max: 350,
                        ticks: {
                            font: {
                                size: 12,
                                weight: 400,
                            },
                            color: '#363636',
                            stepSize: 50,
                            callback: function (value) {
                                return '$' + value;
                            }
                        }
                    }
                }
            }
        };

        const popularServicesChart = new Chart(ctx, config);
    </script>

<?php $__env->stopPush(); ?><?php /**PATH D:\git-file\anders\resources\views/dashboard/templates/index/admin_index.blade.php ENDPATH**/ ?>