<?php
declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON>\JWT;

use <PERSON><PERSON><PERSON><PERSON>\JWT\Validation\Constraint;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Validation\NoConstraintsGiven;
use <PERSON><PERSON><PERSON><PERSON>\JWT\Validation\RequiredConstraintsViolated;

interface Validator
{
    /**
     * @throws RequiredConstraintsViolated
     * @throws NoConstraintsGiven
     */
    public function assert(Token $token, Constraint ...$constraints): void;

    /** @throws NoConstraintsGiven */
    public function validate(Token $token, Constraint ...$constraints): bool;
}
