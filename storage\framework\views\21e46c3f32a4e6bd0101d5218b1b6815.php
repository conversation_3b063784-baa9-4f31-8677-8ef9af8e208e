<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard profile-setting">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row">
                <div class="col-md-12 d-flex justify-content-between align-items-center">
                   <div>
                     <h4 class="sora black">Profile</h4>
                    <p class="fs-14 light-black sora">Lorem ipsum dolor sit amet consectetur. </p>
                   </div>

                    <?php if(auth()->check() && auth()->user()->hasRole('customer')): ?>
                   <div>
                        <button class="drop-btn delete-btn btn btn-outline-danger  py-2 px-3 text-center"><i
                                class="bi bi-trash p-0 red me-3"></i>Delete Profile</button>
                   </div>
                    <?php endif; ?>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="card white-box friends-cards ">
                        <div class="card-header align-items-center justify-content-center flex-column gap-3 py-20">
                            <div class="image-input image-input-circle" data-kt-image-input="true"
                                style="background-image: url('/website/assets/media/avatars/avatar.svg')">
                                <div class="image-input-wrapper w-125px h-125px"
                                    style="background-image: url('/website/assets/media/avatars/avatar.svg')"></div>
                                <label
                                    class="image-button btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px shadow"
                                    data-kt-image-input-action="change" data-bs-toggle="tooltip" data-bs-dismiss="click"
                                    title="Change avatar">
                                    <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span
                                            class="path2"></span></i>

                                    <input type="file" name="avatar" accept=".png, .jpg, .jpeg" />
                                    <input type="hidden" name="avatar_remove" />
                                </label>
                                <span
                                    class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                    data-kt-image-input-action="cancel" data-bs-toggle="tooltip" data-bs-dismiss="click"
                                    title="Cancel avatar">
                                    <i class="ki-outline ki-cross fs-3"></i>
                                </span>
                                <span
                                    class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                    data-kt-image-input-action="remove" data-bs-toggle="tooltip" data-bs-dismiss="click"
                                    title="Remove avatar">
                                    <i class="ki-outline ki-cross fs-3"></i>
                                </span>
                            </div>
                            <p class="fs-22 sora black semi_bold">Roger Press</p>
                        </div>
                        <div class="card-body">
                            <p class="fs-12 normal sora light-black"><span class="me-3"><?php echo $__env->make('svg.building', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                <EMAIL></p>
                            <p class="fs-12 normal sora light-black"><span class="me-3"><?php echo $__env->make('svg.pin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                Menlo Park, United States</p>
                            <p class="fs-12 normal sora light-black"></p><span class="me-3"><i
                                    class="fa-regular fa-star"></i></span>No
                            reviews</p>

                            <div class="d-flex gap-4 mt-5">
                                <a href="https://www.facebook.com/" target="_blank" class="logo-box">
                                    <?php echo $__env->make('svg.fb-logo', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></a>
                                <a href="https://www.instagram.com/" target="_blank" class="logo-box">
                                    <?php echo $__env->make('svg.insta-logo', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></a>
                                <a href="https://www.x.com/" target="_blank" class="logo-box"> <?php echo $__env->make('svg.x-logo', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></a>
                                <a href="https://www.tiktok.com/" target="_blank" class="logo-box">
                                    <?php echo $__env->make('svg.tiktok-logo', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="row row-gap-5">
                        <?php if(Auth::user()->hasAnyRole(['individual', 'business'])): ?>
                            <!-- businesss and individual -->
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Profile Images</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-3 ">
                                                <img src="<?php echo e(asset('website')); ?>/assets/images/professional2.png"
                                                    class="h-100 w-100 rounded-3  object-fit-contain top-rated-image"
                                                    alt="card-image" />
                                            </div>
                                            <div class="col-md-3 ">
                                                <img src="<?php echo e(asset('website')); ?>/assets/images/card-image.png"
                                                    class="h-100 w-100 rounded-3  object-fit-contain top-rated-image"
                                                    alt="card-image" />
                                            </div>
                                            <div class="col-md-3 ">
                                                <img src="<?php echo e(asset('website')); ?>/assets/images/card-image.png"
                                                    class="h-100 w-100 rounded-3 object-fit-contain top-rated-image"
                                                    alt="card-image" />
                                            </div>
                                            <div class="col-md-3 ">
                                                <img src="<?php echo e(asset('website')); ?>/assets/images/card-image.png"
                                                    class="h-100 w-100 rounded-3  object-fit-contain top-rated-image"
                                                    alt="card-image" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Personal Info</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-6 ">
                                                <label for="full-name" class="form-label form-input-labels">Full name</label>
                                                <input type="text" class="form-control form-inputs-field"
                                                    placeholder="Enter full-name" id="full-name" name="full-name"
                                                    value="Roger Press">
                                            </div>
                                            <div class="col-md-6 ">
                                                <label for="email" class="form-label form-input-labels">Email Address</label>
                                                <input type="email" class="form-control form-inputs-field"
                                                    placeholder="Enter email address" id="email" name="email"
                                                    value="<EMAIL>" disabled>
                                            </div>
                                            <div class="col-md-6 ">
                                                <label for="phone-number" class="form-label form-input-labels">Phone
                                                    Number</label>
                                                <input type="tel" class="form-control form-inputs-field"
                                                    placeholder="Enter phone number " id="phone-number" name="phone-number"
                                                    value="+56-955-588-939">
                                            </div>
                                            <div class="col-md-6 ">
                                                <label for="location" class="form-label form-input-labels">Location</label>
                                                <input type="text" class="form-control form-inputs-field"
                                                    placeholder="Enter email address" id="location" name="location"
                                                    value="6391 Elgin St. Celina, Delaware 10299">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Company details</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12 ">
                                                <label for="company-name" class="form-label form-input-labels">Company
                                                    name</label>
                                                <input type="text" class="form-control form-inputs-field"
                                                    placeholder="Enter company name" id="company-name" name="company-name"
                                                    value="Acme - Acme Corporation">
                                            </div>
                                            <div class="col-md-6 ">
                                                <label for="company-id" class="form-label form-input-labels">Company ID</label>
                                                <input type="email" class="form-control form-inputs-field"
                                                    placeholder="Enter company id" id="company-id" name="company-id"
                                                    value="ABC123">
                                            </div>
                                            <div class="col-md-6 ">
                                                <label for="company-vat-number" class="form-label form-input-labels">Company VAT
                                                    number</label>
                                                <input type="tel" class="form-control form-inputs-field"
                                                    placeholder="Enter company vat number " id="company-vat-number"
                                                    name="company-vat-number" value="GB123456789">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Service details</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                <p class="fs-14 black regular">Primary Services</p>
                                                <div class="d-flex gap-4 flex-wrap">
                                                    <?php for($i = 0; $i < 6; $i++): ?>
                                                        <p class="fs-14 sora light-black normal service-details">Makeup Artists</p>
                                                    <?php endfor; ?>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <p class="fs-14 black regular">Secondary Services</p>
                                                <div class="d-flex gap-4 flex-wrap">
                                                    <?php for($i = 0; $i < 6; $i++): ?>
                                                        <p class="fs-14 sora light-black normal service-details">Makeup Artists</p>
                                                    <?php endfor; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Product Certifications</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                <div class="d-flex gap-4 flex-wrap">
                                                    <?php for($i = 0; $i < 8; $i++): ?>
                                                        <p class="fs-14 sora light-black normal service-details align-items-center">
                                                            <span>
                                                                <img src="<?php echo e(asset('website')); ?>/assets/images/certificate.png"
                                                                    class="h-25px w-25px object-fit-contain rounded-pill top-rated-image"
                                                                    alt="card-image" />
                                                            </span>
                                                         <span>   Makeup Artists</span>
                                                        </p>
                                                    <?php endfor; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Certifications & Licenses</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <?php for($i = 0; $i < 2; $i++): ?>
                                                <div class="col-md-6">
                                                    <div
                                                        class="card card-box flex-row gap-3 justify-content-center align-items-center">
                                                        <div
                                                            class="card-header border-0 card-box justify-content-center align-items-center p-7">
                                                            <img src="<?php echo e(asset('website')); ?>/assets/images/licenses.png"
                                                                class="h-50px w-50px object-fit-contain top-rated-image"
                                                                alt="card-image" />
                                                        </div>
                                                        <div class="card-body p-0">
                                                            <p class="black sora fs-16 semi_bold m-0">Certified Stylist Program</p>
                                                            <p class="black fs-14 normal m-0"> <span class="link-gray">Issued by:
                                                                </span>Lorem Ipsum</p>
                                                            <p class="black fs-14 normal m-0"> <span class="link-gray">Issue
                                                                    Date:</span> 08, 11, 2025</p>
                                                            <p class="black fs-14 normal m-0"> <span class="link-gray">End Date:
                                                                </span> 08, 11, 2025</p>

                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endfor; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Availability</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                <p class="fs-14 black regular">Weekly Availability </p>
                                                <div class="d-flex gap-2 flex-wrap">
                                                    <?php for($i = 0; $i < 2; $i++): ?>
                                                        <p class="fs-14 sora light-black normal service-details">Monday (10:00am
                                                            - 7:00pm)</p>
                                                    <?php endfor; ?>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <p class="fs-14 black regular">Holidays</p>
                                                <div class="d-flex gap-4 flex-wrap">
                                                    <?php for($i = 0; $i < 6; $i++): ?>
                                                        <p class="fs-14 sora light-black normal service-details">New Year's Day
                                                            ( January 1)</p>
                                                    <?php endfor; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Intro Card</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0">Add</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <?php for($i = 0; $i < 3; $i++): ?>
                                                <div class="col-md-6">
                                                    <div
                                                        class="card card-box p-0 flex-row justify-content-center align-items-center p-3 gap-5">
                                                        <div
                                                            class="card-header border-0 p-0 justify-content-center align-items-center">
                                                            <img src="<?php echo e(asset('website')); ?>/assets/images/badge.png"
                                                                class="h-35px w-35px  object-fit-contain top-rated-image"
                                                                alt="card-image" />
                                                        </div>
                                                        <div class="card-body p-0">
                                                            <p class="fs-16 sora w-700 m-0 dark-blue">100% Satisfaction
                                                                Guaranteed </p>
                                                            <p class="fs-14 sora normal  m-0 light-gray">Lorem ipsum dolor sit
                                                                amet </p>
                                                        </div>
                                                        <div class="card-footer p-0 border-0">
                                                            <div class="dropdown">
                                                                <a class="drop-btn" type="button" id="dropdownMenuButton"
                                                                    data-bs-toggle="dropdown" aria-expanded="false">
                                                                    <i class="bi bi-three-dots-vertical"></i>
                                                                </a>
                                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton"
                                                                    style="">
                                                                    <li>
                                                                        <button class="dropdown-item complete fs-14 regular "
                                                                            type="button">
                                                                            <i class="bi bi-check-circle complete-icon"></i>
                                                                            Mark as Complete
                                                                        </button>
                                                                    </li>
                                                                    <li>
                                                                        <button class="dropdown-item cancel fs-14 regular"
                                                                            type="button">
                                                                            <i class="fa-solid fa-xmark cancel-icon"></i> Cancel
                                                                        </button>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div
                                                        class="card card-box p-0 flex-row justify-content-center align-items-center p-3 gap-5">
                                                        <div
                                                            class="card-header border-0 p-0 justify-content-center align-items-center">
                                                            <img src="<?php echo e(asset('website')); ?>/assets/images/like-stars.png"
                                                                class="h-35px w-35px  object-fit-contain" alt="card-image" />
                                                        </div>
                                                        <div class="card-body p-0">
                                                            <p class="fs-16 sora w-700 m-0 dark-blue">100% Satisfaction
                                                                Guaranteed </p>
                                                            <p class="fs-14 sora normal  m-0 light-gray">Lorem ipsum dolor sit
                                                                amet </p>
                                                        </div>
                                                        <div class="card-footer p-0 border-0">
                                                            <div class="dropdown">
                                                                <a class="drop-btn" type="button" id="dropdownMenuButton"
                                                                    data-bs-toggle="dropdown" aria-expanded="false">
                                                                    <i class="bi bi-three-dots-vertical"></i>
                                                                </a>
                                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton"
                                                                    style="">
                                                                    <li>
                                                                        <button class="dropdown-item complete fs-14 regular "
                                                                            type="button">
                                                                            <i class="bi bi-check-circle complete-icon"></i>
                                                                            Mark as Complete
                                                                        </button>
                                                                    </li>
                                                                    <li>
                                                                        <button class="dropdown-item cancel fs-14 regular"
                                                                            type="button">
                                                                            <i class="fa-solid fa-xmark cancel-icon"></i> Cancel
                                                                        </button>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endfor; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- end business -->
                        <?php elseif(auth()->check() && auth()->user()->hasRole('customer')): ?>
                            <!-- customer -->
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Personal Info</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0" data-bs-toggle="modal" data-bs-target="#Edit_Personal_Info_ModalLabel">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-6 ">
                                                <label for="customer-full-name" class="form-label form-input-labels">Full
                                                    name</label>
                                                <input type="text" class="form-control form-inputs-field"
                                                    placeholder="Enter full-name" id="customer-full-name"
                                                    name="customer-full-name" value="Roger Press" disabled>
                                            </div>
                                            <div class="col-md-6 ">
                                                <label for="customer-email" class="form-label form-input-labels">Email
                                            </label>
                                                <input type="email" class="form-control form-inputs-field"
                                                    placeholder="Enter email address" id="customer-email"
                                                    name="customer-email" value="<EMAIL>" disabled>
                                            </div>

                                            <div class="col-md-12 ">
                                                <label for="customer-location"
                                                    class="form-label form-input-labels">Location</label>
                                                <input type="text" class="form-control form-inputs-field"
                                                    placeholder="Enter email address" id="customer-location"
                                                    name="customer-location" value="6391 Elgin St. Celina, Delaware 10299" disabled>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Service Preferences</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                <div class="d-flex gap-4 flex-wrap">
                                                    <?php for($i = 0; $i < 4; $i++): ?>
                                                        <p class="fs-14 sora light-black normal service-details">Makeup Artists
                                                        </p>
                                                    <?php endfor; ?>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Hair Type</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                <div class="card flex-row gap-4 border-0 shadow-none">
                                                    <div class="card-header p-0 border-0 align-items-start">
                                                        <img src="<?php echo e(asset('website')); ?>/assets/images/wax.png"
                                                            class="h-125px w-125px rounded-3 object-fit-contain"
                                                            alt="card-image" />
                                                    </div>
                                                    <div class="card-body p-0">
                                                        <p class="fs-16 sora black semi_bold">Curly (Type 3C)</p>
                                                        <p class="fs-12  regular  text-uppercase m-0 link-gray">Description
                                                        </p>
                                                        <p class="fs-15 black normal">Very tight, corkscrew-shaped curls
                                                            with dense texture.</p>
                                                        <p class="fs-12  regular  text-uppercase m-0 link-gray">Be aware</p>
                                                        <p class="fs-15 black normal">Very straight, flat, and fine hair
                                                            prone to looking limp and lacking volume.</p>
                                                        <p class="fs-12  regular  text-uppercase m-0 link-gray">
                                                            Recommendations</p>
                                                        <p class="fs-15 black normal">Use lightweight shampoos and
                                                            conditioners; avoid heavy styling products; minimize heat
                                                            styling; handle gently to prevent breakage.</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Skin Type</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                <div class="card flex-row gap-4 border-0 shadow-none">
                                                    <div class="card-header p-0 border-0 align-items-start">
                                                        <img src="<?php echo e(asset('website')); ?>/assets/images/wax.png"
                                                            class="h-125px w-125px rounded-3 object-fit-contain"
                                                            alt="card-image" />
                                                    </div>
                                                    <div class="card-body p-0">
                                                        <p class="fs-16 sora black semi_bold">Espresso</p>
                                                        <p class="fs-12  regular  text-uppercase m-0 link-gray">Description
                                                        </p>
                                                        <p class="fs-15 black normal">Very tight, corkscrew-shaped curls
                                                            with dense texture.</p>
                                                        <p class="fs-12  regular  text-uppercase m-0 link-gray">Be aware</p>
                                                        <p class="fs-15 black normal">Very straight, flat, and fine hair
                                                            prone to looking limp and lacking volume.</p>
                                                        <p class="fs-12  regular  text-uppercase m-0 link-gray">
                                                            Recommendations</p>
                                                        <p class="fs-15 black normal">Use lightweight shampoos and
                                                            conditioners; avoid heavy styling products; minimize heat
                                                            styling; handle gently to prevent breakage.</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Body Type</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                <div class="card flex-row gap-4 border-0 shadow-none">
                                                    <div class="card-header p-0 border-0 align-items-start">
                                                        <img src="<?php echo e(asset('website')); ?>/assets/images/wax.png"
                                                            class="h-125px w-125px rounded-3 object-fit-contain"
                                                            alt="card-image" />
                                                    </div>
                                                    <div class="card-body p-0">
                                                        <p class="fs-16 sora black semi_bold">Espresso</p>
                                                        <p class="fs-12  regular  text-uppercase m-0 link-gray">Description
                                                        </p>
                                                        <p class="fs-15 black normal">Very tight, corkscrew-shaped curls
                                                            with dense texture.</p>
                                                        <p class="fs-12  regular  text-uppercase m-0 link-gray">Be aware</p>
                                                        <p class="fs-15 black normal">Very straight, flat, and fine hair
                                                            prone to looking limp and lacking volume.</p>
                                                        <p class="fs-12  regular  text-uppercase m-0 link-gray">
                                                            Recommendations</p>
                                                        <p class="fs-15 black normal">Use lightweight shampoos and
                                                            conditioners; avoid heavy styling products; minimize heat
                                                            styling; handle gently to prevent breakage.</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Body Allergies</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                <div class="card flex-row gap-4 border-0 shadow-none">
                                                    <div class="card-header p-0 border-0 align-items-start">
                                                        <img src="<?php echo e(asset('website')); ?>/assets/images/wax.png"
                                                            class="h-125px w-125px rounded-3 object-fit-contain"
                                                            alt="card-image" />
                                                    </div>
                                                    <div class="card-body p-0">
                                                        <p class="fs-16 sora black semi_bold">Body</p>

                                                        <p class="fs-12  regular  text-uppercase m-0 link-gray">Soaps, Body
                                                            Wash, and Shower Gels</p>
                                                        <p class="fs-15 black normal">Sulfates, Fragrances & Perfumes,
                                                            Preservatives</p>
                                                        <p class="fs-12  regular  text-uppercase m-0 link-gray">Lotions &
                                                            Moisturizers</p>
                                                        <p class="fs-15 black normal">Lanolin, Coconut-derived ingredients,
                                                            Shea Butter & Cocoa Butter</p>
                                                        <p class="fs-12  regular  text-uppercase m-0 link-gray">Deodorants &
                                                            Antiperspirants</p>
                                                        <p class="fs-15 black normal">Aluminum compounds, Fragrances &
                                                            Essential Oils, Propylene Glycol</p>
                                                        <p class="fs-12  regular  text-uppercase m-0 link-gray">Sunscreen
                                                            Allergies</p>
                                                        <p class="fs-15 black normal">Oxybenzone, Avobenzone, Octocrylene,
                                                            Homosalate, Titanium Dioxide & Zinc Oxide</p>
                                                        <p class="fs-12  regular  text-uppercase m-0 link-gray">Shaving
                                                            Cream & Aftershave</p>
                                                        <p class="fs-15 black normal">Alcohols, Menthol & Eucalyptus</p>
                                                        <p class="fs-12  regular  text-uppercase m-0 link-gray">Intimate
                                                            Hygiene Products</p>
                                                        <p class="fs-15 black normal">Fragrances & Dyes, Parabens &
                                                            Preservatives</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Common Allergies</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                <div class="card flex-row gap-4 border-0 shadow-none">
                                                    <div class="card-header p-0 border-0 align-items-start">
                                                        <img src="<?php echo e(asset('website')); ?>/assets/images/wax.png"
                                                            class="h-125px w-125px rounded-3 object-fit-contain"
                                                            alt="card-image" />
                                                    </div>
                                                    <div class="card-body p-0">
                                                        <p class="fs-16 sora black semi_bold">Common</p>

                                                        <p class="fs-12  regular  text-uppercase m-0 link-gray">Face Wash &
                                                            Cleansers</p>
                                                        <p class="fs-15 black normal">SLS/SLES, Fragrances & Dyes</p>
                                                        <p class="fs-12  regular  text-uppercase m-0 link-gray">Face Creams
                                                            & Moisturizerss</p>
                                                        <p class="fs-15 black normal">Parabens, Lanolin, Alcohols</p>
                                                        <p class="fs-12  regular  text-uppercase m-0 link-gray">Makeup
                                                            Allergies</p>
                                                        <p class="fs-15 black normal">Fragrances & Dyes, Preservatives,
                                                            Nickel & Heavy Metals, Mascara Ingredients</p>
                                                        <p class="fs-12  regular  text-uppercase m-0 link-gray">Lip Balm &
                                                            Lipstick</p>
                                                        <p class="fs-15 black normal">Propolis, Flavoring agents, Dyes</p>
                                                        <p class="fs-12  regular  text-uppercase m-0 link-gray">Face Masks &
                                                            Peels</p>
                                                        <p class="fs-15 black normal">Face Masks & Peels</p>
                                                        <p class="fs-12  regular  text-uppercase m-0 link-gray">Anti-Aging &
                                                            Acne Treatments</p>
                                                        <p class="fs-15 black normal">Retinoids (Retinol, Tretinoin),
                                                            Benzoyl Peroxide & Salicylic Acid, Hydroquinone</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--  end customer -- -->
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
<?php echo $__env->make('dashboard.templates.modal.profile-setting-edit-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\git-file\anders\resources\views/dashboard/profile_setting.blade.php ENDPATH**/ ?>