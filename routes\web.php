<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\CategoriesController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ThemeController;


use App\Http\Controllers\RoleController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\CrudGeneratorController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\EmailVerificationController;
use App\Http\Controllers\SubCategoriesController;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\WebsiteController;
use App\Http\Controllers\SocialAuthController;
use <PERSON><PERSON>ergmann\CodeCoverage\Report\Html\Dashboard;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/clear-all', function () {
    //$exitCodeConfig = Artisan::call('storage:link');
    $exitCodeRoute = Artisan::call('route:clear');
    $exitCodeCache = Artisan::call('cache:clear');
    $exitCodeUpdate = Artisan::call('optimize:clear');
    $exitCodeView = Artisan::call('view:clear');
    $exitConfigCache = Artisan::call('config:clear');
    // $exitCodePermissionCache = Artisan::call('permission:cache-reset');
    //$exitCodePermissionCache = Artisan::call('cache:forget laravelspatie.permission.cache');
    return '<div style="text-align:center;"> <h1 style="text-align:center;">Cache and Config and permission cache are cleared.</h1><h4><a href="/">Go to home</a></h4></div>';
});

Route::get('/', [WebsiteController::class, 'index'])->name('/');


Route::get('/test', function () {
    $permissions = Permission::pluck('id', 'id')->all();
    return $permissions; // Ensure you are returning the variable here
});
// Route::get('/', function () {
//     return view('website.index');
// });
Route::get('/logout', function () {
    Auth::logout();
    return redirect('/'); // Redirect the user after logout
});



// Zohaib Route
Route::get('send-otp', [EmailVerificationController::class, 'sendOtp'])->name('send_otp');
Route::post('verify-otp', [EmailVerificationController::class, 'verifyOtp'])->name('verify_otp');
Route::post("set-password", [AuthController::class, "set_password"])->name('set_password');



Route::middleware(['auth', "verified"])->group(function () {
    Route::get("register/{user_type}", [AuthController::class, 'registerUserType'])->name('register.user_type')->where("user_type", "customer|professional");
    Route::post("register/customer", [AuthController::class, 'registerCustomer'])->name('register.customer');
    Route::post("register/professional", [AuthController::class, 'registerProfessional'])->name('register.profiessional');
});


Route::get('home', function () {
    if (auth()->user()) {
        return redirect('home');
    } else {
        return view('auth.login');
    }
})->middleware('auth');
Route::get('crud_generator', [CrudGeneratorController::class, 'crudGenerator'])->name('crud_generator');
Route::post('crud_generator_process', [CrudGeneratorController::class, 'crudGeneratorProcess'])->name('crud_generator_process');

Route::get('permissions', [ThemeController::class, 'permissions'])->name('permissions')->middleware('auth');
Auth::routes();
Route::get('/home', [ThemeController::class, 'dashboard'])->name('home')->middleware('auth');
Route::resource("settings", "\App\Http\Controllers\SettingsController")->middleware("auth");
// Route::fallback(function(){
// route(404);
// });

Auth::routes(['verify' => true]);
Route::get('/email/verify', function () {
    return view('auth.verify-email');
})->middleware('auth')->name('verification.notice');

Route::middleware(['auth', "verified", "user_check"])->group(function () {
    Route::get("dashboard", function () {
        return "the email is verified";
    });
    Route::get('/dashboard', [ThemeController::class, 'dashboard'])->name('dashboard');
});


Route::group(['middleware' => ['auth']], function () {
    Route::resource('roles', RoleController::class);
    //Route::resource('roles/{id?}', RoleController::class)->name('roles.edit');
    Route::resource('users', UserController::class);
});
// website  route
Route::get('services', [WebsiteController::class, 'services'])->name('services');
Route::get('professional', [WebsiteController::class, 'professional'])->name('professional');
Route::get('privacy_policy', [WebsiteController::class, 'privacyPolicy'])->name('privacy_policy');
Route::get('terms', [WebsiteController::class, 'terms'])->name('terms');

//customer routes
Route::get('family_friends', [ThemeController::class, 'familyFriends'])->name('family_friends')->middleware('auth');
Route::get('family_friends_details', [ThemeController::class, 'friendsDetails'])->name('family_friends_details')->middleware('auth');
Route::get('add-friends', [ThemeController::class, 'addFriends'])->name('add-friends')->middleware('auth');
Route::get('customer_booking', [ThemeController::class, 'customerBooking'])->name('customer_booking')->middleware('auth');
Route::get('customer_wallet', [ThemeController::class, 'customerWallet'])->name('customer_wallet')->middleware('auth');
Route::get('favorite_professional', [ThemeController::class, 'favoriteProfessional'])->name('favorite_professional')->middleware('auth');
Route::get('cart', [ThemeController::class, 'cart'])->name('cart')->middleware('auth');
Route::get('professional_profile', [ThemeController::class, 'professional_profile'])->name('professional_profile')->middleware('auth');

//Businesss route
Route::get('business_analytics', [ThemeController::class, 'businessAnalytics'])->name('business_analytics')->middleware('auth');
Route::get('booking', [ThemeController::class, 'businessBooking'])->name('booking')->middleware('auth');
Route::get('business_services', [ThemeController::class, 'businessServices'])->name('business_services');
Route::get('add_services', [ThemeController::class, 'addServices'])->name('add_services')->middleware('auth');
Route::get('add_coupon', [ThemeController::class, 'addDiscount'])->name('add_coupon')->middleware('auth');
Route::get('add_staff_member', [ThemeController::class, 'addStaffMember'])->name('add_staff_member')->middleware('auth');
Route::get('business_setting', [ThemeController::class, 'businessSetting'])->name('business_setting');
Route::get('earning', [ThemeController::class, 'businessEarning'])->name('earning')->middleware('auth');
Route::get('staff_member', action: [ThemeController::class, 'staffMember'])->name('staff_member')->middleware('auth');
Route::get('discount_coupon', action: [ThemeController::class, 'discountCoupon'])->name('discount_coupon')->middleware('auth');
Route::get('staff-member-details', [ThemeController::class, 'staffMemberDetails'])->name('staff-member-details')->middleware('auth');

Route::get('subscription', action: [ThemeController::class, 'subscription'])->name('subscription');

Route::get('notification', action: [ThemeController::class, 'notification'])->name('notification')->middleware('auth');
Route::get('profile_setting', action: [ThemeController::class, 'profileSetting'])->name('profile_setting')->middleware('auth');


//Stepper form route
Route::get('professional_account', [ThemeController::class, 'professional_account'])->name('professional_account_stepper');
Route::get('customer/testing', [ThemeController::class, 'testing'])->name('customer.testing');

// admin route
// Route::get('admin_category', [DashboardController::class, 'adminCategory'])->name('admin_category');
Route::get('professionals', [ThemeController::class, 'adminProfessionals'])->name('professionals');
Route::get('customers', [ThemeController::class, 'adminCustomers'])->name('customers');
Route::get('refund_request', [ThemeController::class, 'refundRequest'])->name('refund_request');
Route::get('wallet', [ThemeController::class, 'adminWallet'])->name('wallet');
Route::get('vat_mgmt', [ThemeController::class, 'adminVat'])->name('vat_mgmt');

Route::get('holidays', [ThemeController::class, 'adminHolidays'])->name('holidays');
// Route::get('certifications', [ThemeController::class, 'adminCertifications'])->name('certifications');

Route::resource("categories", "\App\Http\Controllers\CategoriesController")->middleware("auth");
Route::resource("subcategories", "\App\Http\Controllers\SubCategoriesController")->middleware("auth");
Route::post('/categories/update-status', [CategoriesController::class, 'updateStatus'])->name('categories.update-status');
Route::post('/subcategories/update-status', [SubCategoriesController::class, 'subUpdateStatus'])->name('subcategories.update-status');

Route::resource("certifications", "\App\Http\Controllers\CertificationsController")->middleware("auth");

// Google OAuth Routes
Route::get('/auth/google', [SocialAuthController::class, 'redirectToGoogle'])->name('google.login');
Route::get('/auth/google/callback', [SocialAuthController::class, 'handleGoogleCallback']);

// Apple OAuth Routes  
Route::get('/auth/apple', [SocialAuthController::class, 'redirectToApple'])->name('apple.login');
Route::get('/auth/apple/callback', [SocialAuthController::class, 'handleAppleCallback']);