<?php

namespace App\Models;
use App\Traits\HasUuid;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    use HasFactory , HasUuid;
    protected $fillable = [
        'image',
        'alt_tag',
        'image_description',
        'name',
        'slug',
        'status',
    ];

    public function subcategories()
    {
        return $this->hasMany(SubCategory::class);
    }
}
