<?php
declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Hmac;

use <PERSON><PERSON><PERSON><PERSON>\JWT\Signer\Hmac;

final class Sha384 extends Hmac
{
    public function algorithmId(): string
    {
        return 'HS384';
    }

    public function algorithm(): string
    {
        return 'sha384';
    }

    public function minimumBitsLengthForKey(): int
    {
        return 384;
    }
}
