{"name": "league/oauth1-client", "description": "OAuth 1.0 Client Library", "license": "MIT", "require": {"php": ">=7.1||>=8.0", "ext-json": "*", "ext-openssl": "*", "guzzlehttp/guzzle": "^6.0|^7.0", "guzzlehttp/psr7": "^1.7|^2.0"}, "require-dev": {"ext-simplexml": "*", "phpunit/phpunit": "^7.5||9.5", "mockery/mockery": "^1.3.3", "phpstan/phpstan": "^0.12.42", "friendsofphp/php-cs-fixer": "^2.17"}, "scripts": {"analyze": "vendor/bin/phpstan analyse -l 6 src/", "php-cs-fixer:lint": "PHP_CS_FIXER_IGNORE_ENV=1 vendor/bin/php-cs-fixer fix --verbose --dry-run", "php-cs-fixer:format": "PHP_CS_FIXER_IGNORE_ENV=1 vendor/bin/php-cs-fixer fix", "test:unit": "vendor/bin/phpunit --coverage-text --coverage-clover coverage.xml"}, "suggest": {"ext-simplexml": "For decoding XML-based responses."}, "keywords": ["o<PERSON>h", "oauth1", "authorization", "authentication", "idp", "identity", "sso", "single sign on", "bitbucket", "trello", "tumblr", "twitter"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.webcomm.com.au", "role": "Developer"}], "autoload": {"psr-4": {"League\\OAuth1\\Client\\": "src/"}}, "autoload-dev": {"psr-4": {"League\\OAuth1\\Client\\Tests\\": "tests/"}}, "extra": {"branch-alias": {"dev-master": "1.0-dev", "dev-develop": "2.0-dev"}}}