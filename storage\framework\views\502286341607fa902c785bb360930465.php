<div class="container form-card">
    <div class="row">
        <div class="col-12 mb-5 px-0">
            <h2 class="fs-title">What services do you offer?</h2>
            <p>Choose your primary and up to 3 related category</p>
        </div>
    </div>

    <div class="row mb-8">
        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-md-4 mb-8">
                <div class=" services-section custom-radio-group" data-bs-toggle="collapse"
                    data-bs-target="#collapseExample<?php echo e($category->id); ?>" aria-expanded="false" aria-controls="collapseExample<?php echo e($category->id); ?>">
                    <label class="custom-radio ">
                        <input type="checkbox" name="category[]" value="<?php echo e($category->id); ?>">
                        <span class="radio-box"> <img src="<?php echo e(asset('website') . '/' . $category->image ?? ''); ?>"
                                alt="icon" class="service-icon"> <span
                                class="label-text"><?php echo e($category->name ?? ''); ?></span>
                        </span>
                    </label>
                </div>
            </div>
            <?php if($category->subcategories->isNotEmpty()): ?>
            <div class="collapse" id="collapseExample<?php echo e($category->id); ?>">
                <div class="card card-body">
                    <div class="custom-checkbox-group my-5">
                        <?php $__currentLoopData = $category->subcategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subcategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <label class="custom-checkbox">
                                <input type="checkbox" name="subcategory[]" value="<?php echo e($subcategory->id); ?>">
                                <span class="checkbox-label"><?php echo e($subcategory->name ?? ''); ?></span>
                            </label>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>
<?php /**PATH D:\git-file\anders\resources\views/dashboard/templates/professional-acc-stepper/step2.blade.php ENDPATH**/ ?>